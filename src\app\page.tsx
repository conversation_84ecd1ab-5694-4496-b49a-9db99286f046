/* eslint-disable @next/next/no-img-element */
"use client"
import { Button } from '@/components/ui/button'
import '../styles/diagonalBackground.css'
import Input from '@/components/Input'
import AuthContext from '@/provider/AuthContext'
import { clearLetters, cnpjMask, cpfMask } from '@/utils/masks'
import { useContext, useEffect, useState } from 'react'

export default function Home() {
  const { handleSignInUser } = useContext(AuthContext)

  const [document, setDocument] = useState('')
  const [password, setPassword] = useState('')
  const [type, setType] = useState('admin')
  const [loading, setLoading] = useState<boolean>(false)

  const handleSubmit = () => {
    handleSignInUser({
      document: clearLetters(document),
      password,
      setLoading,
      type
    })
  }

  return (
    <>
      <div className='absolute w-full h-screen'>
        <div className="absolute inset-0 bg-half-half w-full"></div>
      </div>
      
      <div className="flex min-h-full h-screen flex-1 flex-col items-center justify-center px-6 py-12 lg:px-8 relative z-10">
        <img
          className="mx-auto h-10 w-auto"
          src="/logo.svg"
          alt="Your Company"
        />
        <div className="md:w-4/12 p-14 m-auto bg-opacity-30 backdrop-blur-sm border border-[#FF9900] rounded-lg shadow-2xl shadow-current bg-zinc-900" style={{borderWidth: '1px'}}>
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <div>
            <div className="mb-2">
              <Input 
                id='document'
                label=''
                name='document'
                placeholder='Documento'
                type='text'
                value={document}
                setValue={(e) => {
                  if(e.length <= 14) {
                    setDocument(cpfMask(e))
                  } else {
                    setDocument(cnpjMask(e))
                  }
                }}
              />
            </div>
          </div>

          <div>
            <div className="mb-5">
              <Input 
                id='password'
                label=''
                name='password'
                placeholder='Senha'
                type='text'
                isPassword={true}
                keyPress={(e) => {
                  if(e.key === 'Enter') {
                    handleSubmit()
                  }
                }}
                value={password}
                setValue={setPassword}
              />
            </div>
          </div>

          <div>
            <Button
              onClick={handleSubmit}
              loading={loading}
              disabled={(document.length === 0 || password.length === 0) || loading }
              size="lg"
              className='w-full'
            >
              Entrar
            </Button>
          </div>
        </div>
        </div>
      </div>
    </>
  )
}
