import NextAuth from "next-auth";

declare module "next-auth" {
  interface session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      document: string;
      roles: Array<{
        name: string;
        roleId: string;
      }>;
      activeProfile: {
        name: string;
        roleId: string;
      };
    };
    accessToken: string;
    refreshToken: string;
  }

  interface User {
    id: string;
    name: string;
    email: string;
    document: string;
    roles: Array<{
      name: string;
      roleId: string;
    }>;
    activeProfile: {
      name: string;
      roleId: string;
    };
    accessToken: string;
    refreshToken: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    document: string;
    roles: Array<{
      name: string;
      roleId: string;
    }>;
    activeProfile: {
      name: string;
      roleId: string;
    };
    accessToken: string;
    refreshToken: string;
  }
}
