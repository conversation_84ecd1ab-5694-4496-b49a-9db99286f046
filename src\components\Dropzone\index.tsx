// TODO: E preciso trocar o tipo Filetype com File
// https://w3c.github.io/FileAPI/#filelist-section

import {
  DocumentArrowDownIcon,
  DocumentCheckIcon,
  DocumentMagnifyingGlassIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "react-toastify";
import { cn } from "@/lib/utils";
interface Dropzone {
  onFileUploaded: Dispatch<SetStateAction<FileList | undefined>>;
  isPdf?: boolean;
  disable?: boolean;
  errorMessage?: string | undefined;
  onRemoveFile?: () => void;
  fileName?: string;
  
}

const Dropzone: React.FC<Dropzone> = ({
  onFileUploaded,
  isPdf = false,
  disable = false,
  errorMessage,
  onRemoveFile,
  fileName,

  
}: Dropzone) => {
  const [selectedFileUrl, setSelectedFileUrl] = useState("");
  const [hoverError, setHoverError] = useState<string | null>(null);
  const [fileNameInternal, setFileNameInternal] = useState<string| undefined>()
  const allowedTypes = useMemo(
    () =>
      isPdf
        ? ["application/pdf"]
        : ["image/png", "image/jpeg", "application/pdf"],
    [isPdf]
  );

  

  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: any[]) => {
      setHoverError(null);
      if (!acceptedFiles || acceptedFiles.length === 0) return;
      const file = acceptedFiles[0];

      if (!allowedTypes.includes(file.type)) {
        toast.error("Tipo de arquivo não suportado.");
        setSelectedFileUrl("");
        return;
      }

      const minSize = 20 * 1024; // 20KB
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size < minSize || file.size > maxSize) {
        toast.error(
          "Tamanho de arquivo exigido tem que ser maior que 20Kb e menor que 2Mb"
        );
        setSelectedFileUrl("");
        return;
      }


      // @ts-ignore
      onFileUploaded([file]);
      const fileUrl = URL.createObjectURL(file);
      setSelectedFileUrl(fileUrl);
      setFileNameInternal(file.name)
    },
    [onFileUploaded, allowedTypes]
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      maxFiles: 1,
      multiple: false,
      onDragEnter: (event) => setHoverError(null),
      onDragLeave: (event) => setHoverError(null),
      onError: (error) => {
        console.log(error);
        if (
          error.message ===
          `Failed to execute 'createObjectURL' on 'URL': Overload resolution failed.`
        ) {
          toast.warning(
            "Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb"
          );
        }
      },
      accept: !isPdf
        ? {
            "image/png": [".png"],
            "image/jpeg": [".jpeg", ".jpg"],
            "application/pdf": [".pdf"],
          }
        : {
            "application/pdf": [".pdf"],
          },
      onFileDialogCancel: () => {
        setSelectedFileUrl("");
      },
      disabled: disable,
    });

  const isRejected = useMemo(() => {
    return isDragActive && (isDragReject || !!hoverError);
  }, [isDragActive, isDragReject, hoverError]);

  return (
    <>
      <div
        {...getRootProps()}
        className={cn(
          "text-white input relative group cursor-pointer rounded-sm bg-[#D9D9D9] px-16 py-10 w-full flex items-center justify-center border-2",
          {
            "border-green-500": isDragActive && !isRejected,
            "border-red-500": isRejected,
            "border-2 border-red-500": errorMessage,
          }
        )}
      >
        <input {...getInputProps()} accept=".png,.jpg,.jpeg,.pdf" />
        {disable ? (
          <DocumentArrowDownIcon width={40} color="#0da34e" />
        ) : selectedFileUrl ? (
          <DocumentCheckIcon width={40} color="#0da34e" />
        ) : (
          <DocumentMagnifyingGlassIcon width={40} color="#515151" />
        )}
        {errorMessage && (
          <div className="absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[120%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block">
            {errorMessage}
          </div>
        )}
      </div>
  
      {selectedFileUrl && (
       <div className="z-50 flex justify-between items-center mt-2 px-2 text-white text-sm bg-[#1a1a1a]  rounded">
       <span className="truncate max-w-[80%]">{fileNameInternal}</span>
       <button
         type="button"
         onClick={() => {
           setFileNameInternal('');
           setSelectedFileUrl('');
           onRemoveFile?.();
         }}
         className="text-red-500 hover:text-red-700 ml-2 flex-shrink-0"
       >
         <TrashIcon className="w-5 h-5 text-red-500 hover:text-red-700" />

       </button>
     </div>
     
      )}
    </>
  );
  
};

export default Dropzone;
