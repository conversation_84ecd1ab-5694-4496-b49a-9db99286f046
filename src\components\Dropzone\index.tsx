// TODO: E preciso trocar o tipo Filetype com File
// https://w3c.github.io/FileAPI/#filelist-section

import {
  DocumentArrowDownIcon,
  DocumentCheckIcon,
  DocumentMagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "react-toastify";
import { cn } from "@/lib/utils";
interface Dropzone {
  onFileUploaded: Dispatch<SetStateAction<FileList | undefined>>;
  isPdf?: boolean;
  disable?: boolean;
  errorMessage?: string | undefined;
}

const Dropzone: React.FC<Dropzone> = ({
  onFileUploaded,
  isPdf = false,
  disable = false,
  errorMessage,
}: Dropzone) => {
  const [selectedFileUrl, setSelectedFileUrl] = useState("");
  const [hoverError, setHoverError] = useState<string | null>(null);

  const allowedTypes = useMemo(
    () =>
      isPdf
        ? ["application/pdf"]
        : ["image/png", "image/jpeg", "application/pdf"],
    [isPdf]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: any[]) => {
      setHoverError(null);
      if (!acceptedFiles || acceptedFiles.length === 0) return;
      const file = acceptedFiles[0];

      if (!allowedTypes.includes(file.type)) {
        toast.error("Tipo de arquivo não suportado.");
        setSelectedFileUrl("");
        return;
      }

      // const minSize = 20 * 1024; // 20KB
      // const maxSize = 200 * 1024 * 1024; // 200MB
      // if (file.size < minSize || file.size > maxSize) {
      //   toast.error(
      //     "Tamanho de arquivo exigido tem que ser maior que 20Kb e menor que 2Mb"
      //   );
      //   setSelectedFileUrl("");
      //   return;
      // }
      // @ts-ignore
      onFileUploaded([file]);
      const fileUrl = URL.createObjectURL(file);
      setSelectedFileUrl(fileUrl);
    },
    [onFileUploaded, allowedTypes]
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      maxFiles: 1,
      multiple: false,
      onDragEnter: (event) => setHoverError(null),
      onDragLeave: (event) => setHoverError(null),
      onError: (error) => {
        console.log(error);
        if (
          error.message ===
          `Failed to execute 'createObjectURL' on 'URL': Overload resolution failed.`
        ) {
          toast.warning(
            "Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb"
          );
        }
      },
      accept: !isPdf
        ? {
            "image/png": [".png"],
            "image/jpeg": [".jpeg", ".jpg"],
            "application/pdf": [".pdf"],
          }
        : {
            "application/pdf": [".pdf"],
          },
      onFileDialogCancel: () => {
        setSelectedFileUrl("");
      },
      disabled: disable,
    });

  const isRejected = useMemo(() => {
    return isDragActive && (isDragReject || !!hoverError);
  }, [isDragActive, isDragReject, hoverError]);

  return (
    <>
      <div
        {...getRootProps()}
        className={cn(
          "text-white input relative group cursor-pointer rounded-sm bg-[#D9D9D9] px-16 py-10 w-full flex items-center justify-center border-2",
          {
            "border-green-500": isDragActive && !isRejected,
            "border-red-500": isRejected,
            "border-2 border-red-500": errorMessage,
          }
        )}
      >
        <input {...getInputProps()} accept=".png,.jpg,.jpeg,.pdf" />
        {disable ? (
          <div>
            <DocumentArrowDownIcon width={40} color="#0da34e" />
          </div>
        ) : selectedFileUrl ? (
          <div className="">
            <DocumentCheckIcon width={40} color="#0da34e" />
          </div>
        ) : (
          <div className="">
            <DocumentMagnifyingGlassIcon width={40} color="#515151" />
          </div>
        )}
        {errorMessage && (
          <div className="absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[120%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block">
            {errorMessage}
          </div>
        )}
      </div>
    </>
  );
};

export default Dropzone;
