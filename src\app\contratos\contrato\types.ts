import { ContractStatus } from "@/functions/formatStatus"
export interface ContractPJ {
  id: string
  investment: {
    value: string
    term: string
    yield: string
    modality: "p2p" | "mutuo"
    purchasedWith: string
    quotesAmount: string
    gracePeriod: string
    status: ContractStatus
    start: string
    end: string
    finalizedAt: string
    type: "p2p" | "mutuo"
  }
  investor: {
      id: string
      type: "business" | "physical"
      name: string
      document: string
      email: string
      companyName: string
      fantasyName: string
      businessType: string
      size: string
      openingDate: string
      responsibleOwner: {
          id: string
          name: string
          document: string
          email: string
          phone: string
          type: string
          rg: string
          occupation: string
          issuingAgency: string
          nationality: string
          motherName: string
          birthDate: string
          address: {
              zipcode: string
              street: string
              number: string
              complement: string
              neighborhood: string
              city: string
              state: string
          }
      },
      businessDocuments: [],
      address: {
          zipcode: string
          street: string
          number: string
          complement: string
          neighborhood: string
          city: string
          state: string
      }
  },
  files: {
      contractPdf: string
      proofPaymentPdf: string
      personalDocument: string
      proofOfResidence: string
  }
}

export interface ContractPF {
  id: string
  investment: {
    value: string
    term: string
    yield: string
    modality: "p2p" | "mutuo"
    purchasedWith: string
    quotesAmount: string
    gracePeriod: string
    status: ContractStatus
    start: string
    end: string
    finalizedAt: string
    type: "p2p" | "mutuo"
  }
  investor: {
    id: string
    type: "physical" | "business"
    name: string
    document: string
    email: string
    phone: string
    rg: string
    occupation: string
    issuingAgency: string
    nationality: string
    motherName: string
    birthDate: string
    address: {
      zipcode: string
      street: string
      number: string
      complement: string
      neighborhood: string
      city: string
      state: string
    }
  }
  files: {
    contractPdf: string
    proofPaymentPdf: string
    personalDocument: string
    proofOfResidence: string
  }
}