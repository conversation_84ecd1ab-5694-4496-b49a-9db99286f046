import * as yup from "yup";
import { clearLetters } from "../masks";

export const schemaGenerateLink = yup
  .object()
  .shape({
    document: yup.string().required("Obrigatório"),
    email: yup
      .string()
      .email("E-mail incorreto")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
      .required("Obrigatório"),
    value: yup.string().required("Obrigatório"),
    term: yup.string().required("Obrigatório"),
    modality: yup.string().required("Obrigatório"),
    yield: yup
      .number()
      .required("Obrigatório")
      .min(0, "O valor mínimo é 0")
      .max(5, "O valor máximo é 5")
      .typeError("O valor deve ser um número válido"),
    purchaseWith: yup.string().required("Obrigatório"),
    amountQuotes: yup.string().default(""),
    startContract: yup.string().required("Obrigatório"),
    endContract: yup.string().required("Obrigatório"),
    profile: yup.string().required("Obrigatório"),
    details: yup.string().notRequired(),
    debenture: yup.string().required("Obrigatório"),
  })
  .required();

export const schemaRegister = yup
  .object()
  .shape({
    name: yup.string().required("Obrigatório"),
    rg: yup.string().required("Obrigatório"),
    document: yup.string().required("Obrigatório"),
    phoneNumber: yup
      .string()
      .min(15, "Número de telefone inválido!")
      .max(15, "Número de telefone inválido!")
      .required("Obrigatório"),
    dtBirth: yup.string().required("Obrigatório"),
    email: yup
      .string()
      .email("E-mail incorreto")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
      .required("Obrigatório"),
    zipCode: yup.string().required("Obrigatório"),
    neighborhood: yup.string().required("Obrigatório"),
    state: yup.string().required("Obrigatório"),
    city: yup.string().required("Obrigatório"),
    complement: yup.string().default(""),
    number: yup.string().required("Obrigatório"),
    value: yup.string().required("Obrigatório"),
    term: yup.string().required("Obrigatório"),
    modality: yup.string().required("Obrigatório"),
    yield: yup
      .number()
      .required("Obrigatório")
      .min(0, "O valor mínimo é 0")
      .max(5, "O valor máximo é 5")
      .typeError("O valor deve ser um número válido"),
    observations: yup.string().default(""),
    purchaseWith: yup.string().required("Obrigatório"),
    amountQuotes: yup.string(),
    initDate: yup.string().required("Obrigatório"),
    endDate: yup.string().required("Obrigatório"),
    gracePeriod: yup.string().required("Obrigatório"),
    profile: yup.string().required("Obrigatório"),
    bank: yup.string().required("Obrigatório"),
    accountNumber: yup.string().required("Obrigatório"),
    agency: yup.string().required("Obrigatório"),
    pix: yup.string().required("Obrigatório"),
    debenture: yup.string().required("Obrigatório"),

    motherName: yup.string(),

    placeOfBirth: yup.string(),
    occupation: yup.string(),
    issuer: yup.string(),

    testifyPrimaryName: yup.string(),
    testifyPrimaryCpf: yup.string(),

    testifySecondaryName: yup.string(),
    testifySecondaryCpf: yup.string(),

    companyAddress: yup.string(),
    companyCity: yup.string(),
    companyUF: yup.string(),
    companyType: yup.string(),
  })
  .required();

export const schemaCreateAditive = yup.object().shape({
  value: yup.string().required("Obrigatório"),
  profile: yup.string().required("Obrigatório"),
  yield: yup
    .number()
    .required("Obrigatório")
    .min(0, "O valor mínimo é 0")
    .max(5, "O valor máximo é 5")
    .typeError("O valor deve ser um número válido"),
  date: yup.string().required("Obrigatório"),
  bank: yup.string().required("Obrigatório"),
  accountNumber: yup.string().required("Obrigatório"),
  agency: yup.string().required("Obrigatório"),
  pix: yup.string().required("Obrigatório"),
});

export const schemaRegisterManual = yup
  .object()
  .shape({
    name: yup.string().required("Obrigatório"),
    document: yup.string().required("Obrigatório"),
    phoneNumber: yup
      .string()
      .max(15, "Número de telefone inválido!")
      .required("Obrigatório"),
    dtBirth: yup.string().required("Obrigatório"),
    email: yup
      .string()
      .email("E-mail incorreto")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
      .required("Obrigatório"),
    zipCode: yup.string().required("Obrigatório"),
    neighborhood: yup.string().required("Obrigatório"),
    state: yup.string().required("Obrigatório"),
    city: yup.string().required("Obrigatório"),
    complement: yup.string().default(""),
    number: yup.string().required("Obrigatório"),
    profile: yup.string().required("Obrigatório"),
    term: yup.string().required("Obrigatório"),
    yield: yup
      .number()
      .required("Obrigatório")
      .min(0, "O valor mínimo é 0")
      .max(5, "O valor máximo é 5")
      .typeError("O valor deve ser um número válido"),
    value: yup.string().required("Obrigatório"),
    bank: yup.string().required("Obrigatório"),
    agency: yup.string().required("Obrigatório"),
    accountNumber: yup.string().required("Obrigatório"),
    pix: yup.string().required("Obrigatório"),
    debenture: yup.string().required("Obrigatório"),
    observations: yup.string().default(""),
    details: yup.string().default(""),
    initDate: yup.string().required("Obrigatório"),
    endDate: yup.string().required("Obrigatório"),
    amountQuotes: yup.string().default(""),
    modality: yup.string().required("Obrigatório"),
    purchaseWith: yup.string().required("Obrigatório"),
    motherName: yup.string().when("document", (document, schema) => {
      if (document[0] && clearLetters(document[0]).length <= 11) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
  })
  .required();

export const schemaRegisterManualBroker = yup
  .object()
  .shape({
    isPf: yup.boolean().default(false),
    birthDate: yup.string().required("Obrigatório"),
    socialName: yup.string(),
    isTaxable: yup.string().required("Obrigatório"),
    fullName: yup.string().required("Obrigatório"),
    cpf: yup.string().required("Obrigatório"),
    email: yup
      .string()
      .email("E-mail incorreto")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
      .required("Obrigatório"),
    phoneNumber: yup.string().required("Obrigatório"),
    motherName: yup.string().required("Obrigatório"),
    pep: yup.string().required("Obrigatório"),
    ownerCep: yup.string().required("Obrigatório"),
    ownerCity: yup.string().required("Obrigatório"),
    ownerState: yup.string().required("Obrigatório"),
    ownerNeighborhood: yup.string().required("Obrigatório"),
    ownerStreet: yup.string().required("Obrigatório"),
    ownerComplement: yup.string().required("Obrigatório"),
    ownerNumber: yup.string().required("Obrigatório"),
    fantasyName: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    cnpj: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    companyName: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessPhoneNumber: yup
      .string()
      .max(15, "Número de telefone inválido!")
      .when("isPf", (isPf, schema) => {
        if (isPf[0] === false) {
          return schema.required("Campo obrigatório");
        } else {
          return schema.notRequired();
        }
      }),
    dtOpening: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessEmail: yup
      .string()
      .when("isPf", (isPf, schema) => {
        if (isPf[0] === false) {
          return schema
          .email("E-mail incorreto")
          .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
          .required("Campo obrigatório");
        } else {
          return schema.notRequired();
        }
      }),
    type: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    size: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessCep: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessCity: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessState: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessNeighborhood: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessStreet: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    businessComplement: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }), 
    businessNumber: yup.string().when("isPf", (isPf, schema) => {
      if (isPf[0] === false) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),
    participationPercentage: yup
      .number()
      .min(0, "O valor mínimo é 0")
      .max(15, "O valor máximo é 15")
      .typeError("O valor deve ser um número válido")
      .when("$hide", {
        is: true,
        then: (schema) => schema.notRequired(),
        otherwise: (schema) => schema.required("Obrigatório"),
      }),
  })
  .required();

export const schemaRegisterLink = yup
  .object()
  .shape({
    name: yup.string().required("Obrigatório"),
    rg: yup.string().required("Obrigatório"),
    document: yup.string().required("Obrigatório"),
    phoneNumber: yup
      .string()
      .max(15, "Número de telefone inválido!")
      .required("Obrigatório"),
    dtBirth: yup.string().required("Obrigatório"),
    email: yup
      .string()
      .email("E-mail incorreto")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
      .required("Obrigatório"),
    zipCode: yup.string().required("Obrigatório"),
    neighborhood: yup.string().required("Obrigatório"),
    state: yup.string().required("Obrigatório"),
    city: yup.string().required("Obrigatório"),
    complement: yup.string().default(""),
    number: yup.string().required("Obrigatório"),
    bank: yup.string().required("Obrigatório"),
    accountNumber: yup.string().required("Obrigatório"),
    agency: yup.string().required("Obrigatório"),
    pix: yup.string().required("Obrigatório"),
    motherName: yup.string().required("Obrigatório"),

    placeOfBirth: yup.string(),
    occupation: yup.string(),
    issuer: yup.string(),

    testifyPrimaryName: yup.string(),
    testifyPrimaryCpf: yup.string(),

    testifySecondaryName: yup.string(),
    testifySecondaryCpf: yup.string(),
  })
  .required();