import {
  AdjustmentsHorizontalIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import { But<PERSON> } from "@/components/ui/button";
import SelectCustom from "@/components/SelectCustom";
import filterStatusContracts from "@/constants/filterStatusContract";
import { DatePicker } from "@/components/ui/datePicker";
import { useEffect, useRef } from "react";

interface FilterData {
  startData: string;
  endData: string;
  type: string;
  status: string;
}

interface IProps {
  activeModal: boolean;
  setActiveModal: (active: boolean) => void;
  filterData: FilterData;
  hidenButton?: boolean;
  children?: React.ReactNode;
  setFilterData: (data: FilterData) => void;
  handleSearch: (signatarie: string, filters: FilterData) => void;
  setPage: (page: number) => void;
  signatarie: string;
}

const defaultFilters: FilterData = {
  startData: "",
  endData: "",
  type: "all",
  status: "Todos",
};

const filterTypeContractOptions = [
  { label: "Todos", value: "all" },
  { label: "Contratos SCP", value: "SCP" },
  { label: "Contratos Mútuos", value: "P2P" },
];

export default function FilterModal({
  activeModal,
  setActiveModal,
  filterData,
  setFilterData,
  handleSearch,
  hidenButton,
  setPage,
  signatarie,
}: IProps) {
  const updateFilter = (newFilters: FilterData) => {
    setFilterData(newFilters);
    setPage(1);
    handleSearch(signatarie, newFilters);
  };
const modalRef = useRef<HTMLDivElement>(null)

useEffect

  return (
    <div className="flex flex-col md:flex-row md:items-center items-end relative gap-5 justify-end">
      <div
        className="flex w-24 md:items-center p-2 rounded-lg bg-[#3A3A3A] cursor-pointer"
        onClick={() => setActiveModal(!activeModal)}
      >
        <AdjustmentsHorizontalIcon width={15} color="#fff" />
        <p className="px-2 text-sm">Filtros</p>
        {activeModal ? (
          <ChevronUpIcon width={15} color="#fff" />
        ) : (
          <ChevronDownIcon width={15} color="#fff" />
        )}
      </div>
      {activeModal && (
        <div className="absolute md:w-[300px] bg-[#3A3A3A] p-5 top-10 rounded-tl-lg rounded-b-lg z-10">
          <div className="flex w-full justify-between items-center">
            <p className="text-base">Filtros</p>
            <div
              className="cursor-pointer"
              onClick={() => setActiveModal(false)}
            >
              <XCircleIcon width={20} />
            </div>
          </div>
          <div className="flex flex-col justify-between mt-2">
            <div className="flex md:flex-row flex-col justify-between mb-4">
              <div className="mb-2 md:mb-0 w-[48%]">
                <p className="text-xs">Início</p>
                <DatePicker
                  value={filterData.startData}
                  setValue={(date) => {
                    const newFilters = {
                      ...filterData,
                      startData: date,
                    };
                    updateFilter(newFilters);
                  }}
                />
              </div>
              <div className="mb-2 md:mb-0 w-[48%]">
                <p className="text-xs">Fim</p>
                <DatePicker
                  value={filterData.endData}
                  setValue={(date) => {
                    const newFilters = {
                      ...filterData,
                      endData: date,
                    };
                    updateFilter(newFilters);
                  }}
                />
              </div>
            </div>
            <div className="w-full">
              <p className="text-xs">Tipo de contrato</p>
              <SelectCustom
                value={filterData.type}
                onChange={(e) => {
                  const newFilters = {
                    ...filterData,
                    type: e.target.value,
                  };
                  updateFilter(newFilters);
                }}
              >
                {filterTypeContractOptions.map((option, index) => (
                  <option key={index} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </SelectCustom>
            </div>
            <div className="w-full mt-4">
              <p className="text-xs">Status do contrato</p>
              <SelectCustom
                value={filterData.status}
                onChange={(e) => {
                  const newFilters = {
                    ...filterData,
                    status: e.target.value,
                  };
                  updateFilter(newFilters);
                }}
              >
                {filterStatusContracts.map((status, index) => (
                  <option key={index} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </SelectCustom>
            </div>
          </div>
          {!hidenButton && (
            <div className="m-auto mt-5 flex gap-2">
              {(filterData.startData !== "" ||
                filterData.endData !== "" ||
                filterData.type !== "all" ||
                filterData.status !== "Todos") && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setFilterData(defaultFilters);
                    setPage(1);
                    handleSearch(signatarie, defaultFilters);
                    setActiveModal(false);
                  }}
                  className="w-full"
                >
                  Resetar
                </Button>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
