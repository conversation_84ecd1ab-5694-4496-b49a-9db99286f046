import NextAuth, { AuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

export const authConfig: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        document: { label: "Document", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.document || !credentials?.password) {
          return null;
        }

        try {
          // 1. Fazer login para pegar os tokens
          const loginResponse = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/auth-backoffice/login`,
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                document: credentials.document,
                password: credentials.password,
              }),
            }
          );

          if (!loginResponse.ok) {
            return null;
          }

          const loginData = await loginResponse.json();
          const { accessToken, refreshToken } = loginData;

          // 2. Buscar dados do perfil do usuário
          const profileResponse = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/account/profile`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${accessToken}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (!profileResponse.ok) {
            return null;
          }

          const profileData = await profileResponse.json();

          // 3. Filtrar roles (remover 'investor')
          const validRoles = profileData.roles.filter(
            (role: any) => role.name !== "investor"
          );

          if (validRoles.length === 0) {
            return null;
          }

          // 4. Definir role ativo (priorizar superadmin)
          const superAdminRole = validRoles.find(
            (role: any) => role.name === "superadmin"
          );
          const activeRole = superAdminRole || validRoles[0];

          // 5. Retornar dados do usuário para o JWT
          return {
            id: profileData.id,
            name: profileData.name,
            email: profileData.document, // Usar document como email (NextAuth precisa)
            document: profileData.document,
            roles: validRoles,
            activeProfile: {
              name: activeRole.name,
              roleId: activeRole.roleId, // Confirmado que existe
            },
            accessToken,
            refreshToken,
          };
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      },
    }),
  ],

  callbacks: {
    async jwt({ token, user }) {
      // Na primeira autenticação, user contém os dados do authorize()
      if (user) {
        token.id = user.id;
        token.document = user.document;
        token.roles = user.roles;
        token.activeProfile = user.activeProfile;
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
      }

      return token;
    },

    async session({ session, token }) {
      // Passar dados do JWT para a session (disponível no frontend)
      if (!token || !session?.user) {
        return session;
      }

      try {
        // Atribuir propriedades do token para session.user
        session.user.id = String(token.id || "");
        session.user.document = String(token.document || "");
        session.user.roles = Array.isArray(token.roles) ? token.roles : [];
        session.user.activeProfile = token.activeProfile || {
          name: "",
          roleId: "",
        };

        // Adicionar tokens à session
        session.accessToken = String(token.accessToken || "");
        session.refreshToken = String(token.refreshToken || "");
      } catch (error) {
        console.error("Error in session callback:", error);
      }

      return session;
    },
  },

  pages: {
    signIn: "/login", // Sua página de login customizada
  },

  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 dias (sessão não expira)
  },

  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authConfig);
export { handler as GET, handler as POST };
