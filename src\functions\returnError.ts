import { toast } from "react-toastify";

export default function returnError(error: any, message: string) {
  if(typeof error?.response?.data?.message === 'object') {
    const errorArray: string[] = error.response.data.message
    errorArray.map(errorMessage => {
      toast.error(errorMessage)
    })
  } else if(error?.response?.data?.message){
    toast.error(error.response.data.message, {
      toastId: error.response.data.message
    })
  } else {
    toast.error(message, {
      toastId: message
    })
  }
}