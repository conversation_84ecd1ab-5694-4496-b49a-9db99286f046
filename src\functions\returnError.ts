import { toast } from "react-toastify";

export default function returnError(error: any, message: string) {
const apiMessage = error?.response?.data?.message || error?.response?.data?.error

if (Array.isArray(apiMessage)) {
  apiMessage.forEach((msg: string) => {
    toast.error(msg, { toastId: msg });
  });
  return apiMessage.join("\n");
}

if (typeof apiMessage === 'string') {
  toast.error(apiMessage, { toastId: apiMessage });
  return apiMessage;
}

if (typeof apiMessage === 'object' && apiMessage !== null) {
  const parsedMessages = Object.values(apiMessage).flat().join('\n');
  toast.error(parsedMessages, { toastId: parsedMessages });
  return parsedMessages;
}

toast.error(message, { toastId: message  });
return message;
}
