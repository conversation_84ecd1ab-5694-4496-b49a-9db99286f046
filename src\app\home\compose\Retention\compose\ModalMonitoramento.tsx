import Button from "@/components/Button/Button";
import Dropzone from "@/components/Dropzone";
import { RetentionContracts } from "../index";
import { useState } from "react";
import ContactIcon from '@/assets/Icons/contact.svg'
import EmailIcon from '@/assets/Icons/email.svg'
import Image from "next/image";
import { UserProfile } from "@/models/user";
import { toast } from "react-toastify";
import api from "@/core/api";
import Input from "@/components/Input";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { schemaCreateAditive } from "@/utils/schemas/schemasValidation";
import { valueMask } from "@/utils/masks";
import { signIca } from "@/constants/signIca";
import moment from "moment";
import { getUserProfile } from "@/functions/getUserData";

interface IProps {
  loading: boolean
  setModal: (data: any) => void
  setModalContract: (data: any) => void
  setModalType: (data: any) => void
  status: {
    title: string,
    value: string
  }
  contract: RetentionContracts | null
  typeContract: string
}

export default function ModalMonitoramento({setModal, status, contract, setModalContract, setModalType, typeContract}: IProps) {
  const [newContract, setNewContract] = useState<FileList>()
  const [loading, setLoading] = useState(false)
  const [comment, setComment] = useState('')
  const [option, setOption] = useState({
    title: '',
    value: ''
  })
  const [data, setData] = useState({
    value: '',
    profile: '',
    yield: '',
    date: '',
    bank: '',
    agency: '',
    accountNumber: '',
    pix: '',
  })

  const userProfile = getUserProfile()

  const renewContract = () => {
    const form = new FormData()

    if(option.value === '')  {
      return toast.warning(`Selecione uma atualização para o contrato`)
    }

    form.append('status', option.value)
    form.append('comment', comment)
    form.append('ownerRoleId', userProfile.roleId)

    if (newContract) {
      form.append('attachments', newContract[0])
    }

    setLoading(true)
    api.post(`/contract-lifecycle-monitoring/${contract?.id}/retention/update`, form).then(resp => {
      toast.success('Operação realizada com sucesso!')
      setLoading(false)
      window.location.reload()
    }).catch(err => {
      toast.error(err?.response?.data?.message || "Não foi possivel completar a operação")
      setLoading(false)
    })
  }

  const createAditive = () => {
    setLoading(true)
    const payload = {
      roleId: contract?.investorId,
      contractId: contract?.id,
      investment: {
        value: Number(data.value.replace('.', '').replace(',', '.')),
        profile: data.profile,
        yield: Number(data.yield),
        date: moment().format('YYYY-MM-DD')
      },
      accountBank: {
        bank: data.bank,
        accountNumber: data.accountNumber,
        agency: data.agency,
        pix: data.pix
      },
      observations: comment,
      signIca: signIca
    }

    api.post('/contract/additive', payload).then(resp => {
      renewContract()
    }).catch(error => {
      toast.error(error.message || 'Não conseguimos criar o contrato de aditivo!')
      setLoading(false)
    })
  }

  return (
    <div className='z-10 w-10/12 bg-[#000000] fixed top-0 right-0 bottom-0 text-white overflow-auto h-auto p-10 border-l'>
      <div className='w-full text-center'>
        <h1 className='text-2xl font-bold'>{typeContract}</h1>
      </div>
      {
        typeContract === 'À vencer' && (
          <div className='flex mt-5'>
            <div className='bg-[#1E1E1E94] m-auto px-5 py-1 rounded-lg'>
              <p className=''><b className='text-[#FFF700]'>Aviso:</b> está chegando o vencimento do contrato.</p>
            </div>
          </div>
        )
      }
      <div>
        <h2>Dados Pessoais</h2>
        <div className='flex justify-between w-full mt-2'>
          <div>
            <p className='text-sm font-bold'>E-mail</p>
            <p className='text-sm'>{contract?.investorEmail}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>CPF</p>
            <p className='text-sm'>{contract?.investorCpf}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>RG</p>
            <p className='text-sm'>{'Não informado'}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>Perfil</p>
            <p className='text-sm'>{'Não informado'}</p>
          </div>
        </div>
      </div>
      <div className='mt-10'>
        <h2>Informações de Endereço</h2>
        <div className='flex justify-between w-full mt-2'>
          <div>
            <p className='text-sm font-bold'>Endereço</p>
            <p className='text-sm'>{contract?.investorAddresses[0]?.street || 'Não informado'}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>Número</p>
            <p className='text-sm'>{contract?.investorAddresses[0]?.number || 'Não informado'}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>Complemento</p>
            <p className='text-sm'>{contract?.investorAddresses[0]?.complement || 'Não informado'}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>Bairro</p>
            <p className='text-sm'>{contract?.investorAddresses[0]?.neighborhood || 'Não informado'}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>Cidade</p>
            <p className='text-sm'>{contract?.investorAddresses[0]?.city || 'Não informado'}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>UF</p>
            <p className='text-sm'>{contract?.investorAddresses[0]?.state || 'Não informado'}</p>
          </div>
          <div>
            <p className='text-sm font-bold'>CEP</p>
            <p className='text-sm'>{contract?.investorAddresses[0]?.zipCode || 'Não informado'}</p>
          </div>
        </div>
      </div>
      <div className="my-10">
        <p>Observação</p>
        <p className="mt-2 rounded-lg border w-6/12 bg-zinc-950 p-2 border-[#FFB238]">{contract?.latestEvent.comment || 'Nenhuma observação'}</p>
      </div>
      <div className="w-6/12">
        <p className="font-semibold">Clique em um dos botões abaixo para ser direcionado aos meios de contato do investidor e iniciar a comunicação.</p>
        <div className="flex mt-5 gap-4">
          {
            contract?.investorPhone && (
              <div className="">
                <div 
                  className="bg-[#313131] rounded-lg border border-[#FFB238] flex flex-col items-center justify-center cursor-pointer w-24 h-20"
                  onClick={() => {
                    const whatsappUrl = `https://wa.me/${contract?.investorPhone}`;
                    window.open(whatsappUrl, '_blank')
                  }}
                >
                  <Image src={ContactIcon} alt="" width={30} />
                </div>
                <p className="text-sm mt-2 text-center">WhatsApp</p>
              </div>
            )
          }
          {
            contract?.investorEmail && (
              <div className="">
                <div 
                  className="bg-[#313131] rounded-lg border border-[#FFB238] flex flex-col items-center justify-center cursor-pointer  w-24 h-20"
                  onClick={() => {
                    window.open(`mailto:${contract?.investorEmail}`, '_blank')
                  }}
                >
                  <Image src={EmailIcon} alt="" width={30} />
                </div>
                <p className="text-sm mt-2 text-center">E-mail</p>
              </div>
            )
          }
        </div>
      </div>
      {
        contract?.latestEvent.status === 'SENT_TO_RETENTION' && (
          <>
            <div className="my-5">
              <p className="font-semibold text-xl mb-3">Atualização de Status</p>
              <p className="text-sm mb-3">Clique na Opção Escolhida pelo Investidor</p>
              
              <div className="flex gap-1 my-1 cursor-pointer" onClick={() => {
                setOption({
                  title: 'Realizar Aditivo',
                  value: 'CONTRACT_ADDENDUM_REQUESTED'
                })
              }}>
              <input checked={option.value === 'CONTRACT_ADDENDUM_REQUESTED'} type="radio" className="" />
                <p className="text-sm">Realizar Aditivo</p>
              </div>
              <div className="flex gap-1 my-1 cursor-pointer" onClick={() => {
                setOption({
                  title: 'Realizar Renovação de Contrato',
                  value: 'RENEWAL_REQUESTED'
                })
              }}>
              <input checked={option.value === 'RENEWAL_REQUESTED'} type="radio" className="" />
                <p className="text-sm">Realizar Renovação de Contrato</p>
              </div>
              <div className="flex gap-1 my-1 cursor-pointer" onClick={() => {
                setOption({
                  title: 'Realizar Resgate',
                  value: 'REDEMPTION_REQUESTED'
                })
              }}>
                <input checked={option.value === 'REDEMPTION_REQUESTED'}type="radio" className="" />
                <p className="text-sm">Realizar Resgate</p>
              </div>
            </div>
            {
              option.value === 'CONTRACT_ADDENDUM_REQUESTED' && (
                <>
                  <div className="mb-5">
                    <p className="mb-3 text-2xl">Dados de Investimento</p>
                    <div className="flex flex-wrap gap-4">
                      <Input
                        id=""
                        label="Valor do investimento"
                        name=""
                        type="text"
                        value={data.value}
                        onChange={(e) => {
                          setData({
                            ...data,
                            value: valueMask(e.target.value)
                          })
                        }}
                      />
                      <Input
                        id=""
                        label="Taxa de Remuneração Mensal %"
                        name=""
                        type="text"
                        value={data.yield}
                        onChange={(e) => {
                          setData({
                            ...data,
                            yield: e.target.value
                          })
                        }}
                      />
                      <Input
                        id=""
                        label="Perfil investidor"
                        name=""
                        type="text"
                        value={data.profile}
                        onChange={(e) => {
                          setData({
                            ...data,
                            profile: e.target.value
                          })
                        }}
                      />
                    </div>
                  </div>
                  <div className="mb-10">
                    <p className="mb-3 text-2xl">Dados bancarios</p>
                    <div className="flex flex-wrap gap-4">
                      <Input
                        id=""
                        label="Nome do banco"
                        name=""
                        type="text"
                        value={data.bank}
                        onChange={(e) => {
                          setData({
                            ...data,
                            bank: e.target.value
                          })
                        }}
                      />
                      <Input
                        id=""
                        label="Conta"
                        name=""
                        type="text"
                        value={data.accountNumber}
                        onChange={(e) => {
                          setData({
                            ...data,
                            accountNumber: e.target.value
                          })
                        }}
                      />
                      <Input
                        id=""
                        label="Agência"
                        name=""
                        type="text"
                        value={data.agency}
                        onChange={(e) => {
                          setData({
                            ...data,
                            agency: e.target.value
                          })
                        }}
                      />
                      <Input
                        id=""
                        label="Chave pix"
                        name=""
                        type="text"
                        value={data.pix}
                        onChange={(e) => {
                          setData({
                            ...data,
                            pix: e.target.value
                          })
                        }}
                      />
                    </div>
                  </div>
                </>
              )
            }
            <div className="w-6/12">
              <p>Observações *</p>
              <textarea
                value={comment}
                onChange={({target}) => setComment(target.value)}
                className="w-full text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 p-2 mt-2"
              />
            </div>
            <div className='flex flex-col text-white mt-5'>
                <p className=''>Anexar arquivo</p>
                <p className="text-sm text-zinc-500 mb-2">*Anexe aqui qualquer arquivo que considere importante.</p>
              <div className="w-28">
                <Dropzone onFileUploaded={setNewContract} />
              </div>
            </div>
          </>
        )
      }

      <div className='w-full flex justify-end gap-4'>
        <div className=''>
          <Button
            label='Fechar'
            loading={false}
            className='bg-zinc-700'
            disabled={false}
            handleSubmit={() => setModal(false)}
          />
        </div>
        {
          contract?.latestEvent.status === 'SENT_TO_RETENTION' && (
            <div>
              <Button
                label="Enviar"
                loading={loading}
                className='bg-orange-linear'
                disabled={loading}
                handleSubmit={() => {
                  if(option.value === 'CONTRACT_ADDENDUM_REQUESTED') {
                    createAditive()
                  } else {
                    renewContract()
                  }
                }}
              />
            </div>
          )
        }
      </div>
    </div>
  )
}