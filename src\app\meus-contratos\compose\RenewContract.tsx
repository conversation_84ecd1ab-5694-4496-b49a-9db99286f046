import Dropzone from "@/components/Dropzone";
import api from "@/core/api";
import Contact from "@/models/contract";
import moment from "moment";
import { useState } from "react";
import { toast } from "react-toastify";

interface IProps {
  contract: Contact
  setOpenModal: (d: any) => void
}

export default function RenewContract({ contract, setOpenModal }: IProps) {
  const [oldContract, setOldContract] = useState<FileList>()
  const [newContract, setNewContract] = useState<FileList>()
  const [initialDate, setInitialDate] = useState(moment().format("YYYY-MM-DD"))
  const [finalDate, setFinalDate] = useState(moment().format("YYYY-MM-DD"))
  const [oldContractName, setOldContractName] = useState<string>()
  const [newContractName, setNewContractName] = useState<string>()


  const renewContract = () => {
    if(initialDate === null) {
      return toast.warning("Precisa informar a data de inicio de contrato")
    }

    const form = new FormData()
    form.append('newStartDate', initialDate)
    form.append('newEndDate', finalDate)
    if (oldContract) {
      form.append('oldContractPdf', oldContract[0])
    }
    if (newContract) {
      form.append('newContractPdf', newContract[0])
    }
    api.patch(`/contract/renew/${contract.id}`, form).then(resp => {
      toast.success('Contrato atualizado com sucesso!')
      setOpenModal(false)
    }).catch(err => {
    })
  }

  const handleRemoveNewContract = () => {
      setNewContract(undefined);
  }

  const handleRemoveOldContract = () => {
    setOldContract(undefined)
  }

  return (
    <div className="fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10">
      <div className="w-5/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]">
        <p className="text-lg font-bold">Renovação de contrato</p>

        <div className="flex gap-4 mt-5">
        <div className='md:w-2/4'>
          <p className="text-white mb-1">Inicio do contrato</p>
          <input
            value={initialDate}
            onChange={({target}) => setInitialDate(target.value)}
            className={`h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]`}
            type="date"
          />
        </div>
        <div className='md:w-2/4'>
          <p className="text-white mb-1">Fim do contrato</p>
          <input
            value={finalDate}
            onChange={({target}) => setFinalDate(target.value)}
            className={`h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]`}
            type="date"
          />
        </div>
        </div>

        <div className='flex gap-2 mb-10 text-white items-center justify-around mt-5'>
          <div>
            <p className='mb-1'>Contrato antigo</p>
            <Dropzone 
            onFileUploaded={setOldContract} 
            fileName={oldContractName}
            onRemoveFile={handleRemoveOldContract}
            />
          </div>
          <div>
            <p className='mb-1'>Novo Contrato</p>
            <Dropzone 
            onFileUploaded={setNewContract} 
            fileName={newContractName}
            onRemoveFile={handleRemoveNewContract}
            />
          </div>
        </div>
        <div className='flex mt-10 gap-4 justify-end'>
          <div className='px-10 bg-orange-linear flex items-center cursor-pointer' onClick={renewContract}>
            <p className='text-sm'>Renovar contrato</p>
          </div>
          {/* <div className='bg-[#D34653] px-4 flex items-center justify-center cursor-pointer'>
            <p className='text-sm'>Cancelar Contrato</p>
          </div> */}
          <div className='px-5 py-2 bg-[#313131] cursor-pointer' onClick={() => setOpenModal(false)}>
            <p className='text-sm'>Fechar</p>
          </div>
        </div>
      </div>
    </div>
  )
}