"use client";

import InputText from "@/components/Inputs/InputText";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import Button from "@/components/Button/Button";
import {
  cepMask,
  clearLetters,
  cpfMask,
  phoneMask,
  valueMask,
} from "@/utils/masks";
import { createPFContract } from "@/utils/schemas/createContract";
import { useEffect, useState } from "react";
import InputSelect from "@/components/Inputs/InputSelect";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import { toast } from "react-toastify";
import SelectSearch from "@/components/SelectSearch";
import Input from "@/components/Input";
import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import CoverForm from "@/components/CoverForm";
import { getFinalDataWithMount } from "@/functions/getDataFilter";
import moment from "moment";
import formatNumberValue from "@/utils/formatNumberValue";
import { isValidateCPF } from "@/utils/validate-documents";
import { isUnderage } from "@/utils/isUnderage";
import { getUserProfile } from "@/functions/getUserData";
import validatePhoneNumber from "@/utils/validatePhoneNumber";
import isValidUF from "@/utils/isValidUf";
import Dropzone from "@/components/Dropzone";
import { getZipCode } from "@/functions/getZipCode";
import SelectCustom from "@/components/SelectCustom";

interface IProps {
  modalityContract: "MUTUO" | "SCP";
}

interface AdvisorsTableItem {
  id: string;
  taxValue: number | string;
  generatedId: string;
}

interface BrokerAcessor {
  name: string;
  id: string;
  rate: string;
}

export default function PhysicalRegister({ modalityContract }: IProps) {
  const [loading, setLoading] = useState(false);
  const [zipCode, setZipCode] = useState("");

  const [acessors, setAcessors] = useState<BrokerAcessor[]>([]);

  const [brokers, setBrokers] = useState<BrokerAcessor[]>([]);
  const [brokerIdSelected, setBrokerIdSelected] = useState("");

  const [loadingAcessors, setLoadingAcessors] = useState(false);
  const [advisors, setAdvisors] = useState<AdvisorsTableItem[]>([]);

  const [contract, setContract] = useState<FileList>();
  const [file, setFile] = useState<FileList>();
  const [document, setDocumet] = useState<FileList>();
  const [residence, setResidence] = useState<FileList>();

  const userProfile = getUserProfile();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver(createPFContract),
    mode: "all",
    defaultValues: {
      isSCP: modalityContract === "SCP",
      purchaseWith: "pix",
      profile: "moderate",
      isDebenture: "s",
    },
  });

  const initDate = watch("initDate");
  const term = watch("term");

  useEffect(() => {
    if (initDate && term) {
      const endData = getFinalDataWithMount({
        investDate: term,
        startDate: initDate,
      });
      setValue("endDate", endData);
    }
  }, [initDate, term, setValue]);

  const onSubmit = (data: any) => {
    const yeld = Number(data.yield.replace(",", "."));

    setLoading(true);

    const form = new FormData();

    form.append("role", userProfile.name);
    if (userProfile.name !== "broker") {
      form.append("brokerId", brokerIdSelected);
    }

    form.append("personType", "PF");
    form.append("contractType", modalityContract);

    // Dados do investidor
    form.append("individual[fullName]", String(data.name).trim());
    form.append("individual[cpf]", clearLetters(data.document).trim());
    form.append("individual[rg]", data.rg);
    form.append("individual[issuingAgency]", data.issuer);
    form.append("individual[nationality]", data.placeOfBirth);
    form.append("individual[occupation]", data.occupation);
    form.append("individual[birthDate]", data.dtBirth);
    form.append("individual[email]", data.email);
    form.append("individual[phone]", `55${clearLetters(data.phoneNumber)}`);
    form.append("individual[motherName]", data.motherName);

    // Endereço
    form.append("individual[address][street]", data.street);
    form.append("individual[address][neighborhood]", data.neighborhood);
    form.append("individual[address][city]", data.city);
    form.append("individual[address][state]", data.state);
    form.append("individual[address][postalCode]", clearLetters(data.zipCode));
    form.append("individual[address][number]", data.number);
    form.append("individual[address][complement]", data.complement);

    // Dados bancarios
    form.append("bankAccount[bank]", data.bank);
    form.append("bankAccount[agency]", data.agency);
    form.append("bankAccount[account]", data.accountNumber);
    form.append("bankAccount[pix]", data.pix);
    form.append("bankAccount[accountType]", "CORRENTE");

    // Dados do investimento
    form.append("investment[amount]", String(formatNumberValue(data.value)));
    form.append("investment[monthlyRate]", String(yeld));
    form.append("investment[durationInMonths]", data.term);
    form.append("investment[paymentMethod]", data.purchaseWith);
    form.append("investment[startDate]", `${data.initDate}T00:00:00.000Z`);
    form.append(
      "investment[endDate]",
      `${moment(data.endDate, "DD/MM/YYYY").format("YYYY-MM-DD")}T00:00:00.000Z`
    );
    form.append("investment[profile]", data.profile);
    form.append(
      "investment[isDebenture]",
      data.isDebenture === "s" ? "true" : "false"
    );
    if (modalityContract === "SCP") {
      form.append("investment[quotaQuantity]", data.amountQuotes);
    }

    // Adicionando os assessores.
    if (advisors.length === 1) {
      advisors.map((advisor, index) => {
        form.append(`advisors[${index}][advisorId]`, advisor.id);
        form.append(`advisors[${index}][rate]`, String(advisor.taxValue));
      });
    } else {
      if (advisors.length > 1 && advisors[0].id !== "") {
        advisors.map((advisor, index) => {
          form.append(`advisors[${index}][advisorId]`, advisor.id);
          form.append(`advisors[${index}][rate]`, String(advisor.taxValue));
        });
      }
    }

    // Documentos
    if (contract) {
      form.append("contract", contract[0]);
    }
    if (file) {
      form.append("proofOfPayment", file[0]);
    }
    if (document) {
      form.append("personalDocument", document[0]);
    }
    if (residence) {
      form.append("proofOfResidence", residence[0]);
    }

    api
      .post("/account/create/existing-contract", form)
      .then((resp) => {
        toast.success("Investidor cadastrado com sucesso!");
        reset();
        setAdvisors([]);
        setDocumet(undefined);
        setResidence(undefined);
        setContract(undefined);
        setFile(undefined);
      })
      .catch((err) => {
        returnError(err, "Erro ao cadastrar o contrato!");
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    getCep(zipCode || "");
  }, [zipCode]);

  const getCep = async (cep: string) => {
    const response = await getZipCode(clearLetters(cep));

    if (response !== null) {
      setValue("neighborhood", response.neighborhood, {shouldValidate: true});
      setValue("city", response.city, {shouldValidate: true});
      setValue("state", response.state, {shouldValidate: true});
      setValue("street", response.street, {shouldValidate: true});
    }
  };

  function getRouteAdivsorSearch() {
    switch (userProfile.name) {
      case "superadmin":
        return "/wallets/list-advisors-broker";
      case "admin":
        return "/wallets/admin/advisors-broker";
      case "broker":
        return "/wallets/broker/advisors";
      default:
        return "";
    }
  }

  function getAcessors() {
    setLoadingAcessors(true);
    api
      .get(getRouteAdivsorSearch(), {
        params: {
          brokerId:
            userProfile.name !== "broker" ? brokerIdSelected : undefined,
        },
      })
      .then((resp) => {
        setAcessors(resp.data);
      })
      .catch((error) => {
        returnError(error, "Erro ao buscar a lista de brokers");
      })
      .finally(() => setLoadingAcessors(false));
  }

  useEffect(() => {
    if (userProfile.name !== "broker" && userProfile.name !== "advisor") {
      getBrokers();
    }
  }, []);

  useEffect(() => {
    if (
      (userProfile.name !== "broker" && brokerIdSelected !== "") ||
      userProfile.name === "broker"
    ) {
      getAcessors();
    }
  }, [brokerIdSelected]);

  function getBrokers() {
    api
      .get(
        userProfile.name === "superadmin"
          ? "/wallets/list-brokers"
          : "/wallets/admin/brokers"
      )
      .then((resp) => {
        setBrokers(resp.data);
      })
      .catch((error) => {
        returnError(error, "Erro ao buscar a lista de brokers");
      });
  }

  const handleInputChange = (id: string, value: string) => {
    setAdvisors((prevItems) =>
      prevItems.map((item) =>
        item.generatedId === id ? { ...item, taxValue: value } : item
      )
    );
  };

  return (
    <div>
      <form
        action=""
        onSubmit={handleSubmit(onSubmit)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault(); // Bloqueia o envio ao pressionar Enter
          }
        }}
      >
        <CoverForm color="black" title="Dados Pessoais">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="name"
              width="300px"
              error={!!errors.name}
              errorMessage={errors?.name?.message}
              label="Nome completo"
            />
            <InputText
              register={register}
              name="document"
              width="200px"
              error={!!errors.document}
              errorMessage={errors?.document?.message}
              label="CPF"
              setValue={(e) => setValue("document", cpfMask(e || ""), {shouldValidate: true})}
            />
            <InputText
              register={register}
              name="rg"
              width="200px"
              error={!!errors.rg}
              errorMessage={errors?.rg?.message}
              label="Identidade"
            />
            <InputText
              register={register}
              name="issuer"
              width="200px"
              error={!!errors.issuer}
              errorMessage={errors?.issuer?.message}
              label="Orgão emissor"
            />
            <InputText
              register={register}
              name="placeOfBirth"
              width="200px"
              error={!!errors.placeOfBirth}
              errorMessage={errors?.placeOfBirth?.message}
              label="Nacionalidade"
            />
            <InputText
              register={register}
              name="occupation"
              width="200px"
              error={!!errors.occupation}
              errorMessage={errors?.occupation?.message}
              label="Ocupação"
            />
            <InputText
              width="200px"
              register={register}
              name="phoneNumber"
              maxLength={15}
              error={!!errors.phoneNumber}
              errorMessage={errors?.phoneNumber?.message}
              label="Celular"
              setValue={(e) => setValue("phoneNumber", phoneMask(e || ""), {shouldValidate: true})}
            />
            <InputText
              type="date"
              register={register}
              name="dtBirth"
              width="200px"
              error={!!errors.dtBirth}
              errorMessage={errors?.dtBirth?.message}
              label="Data de Nascimento"
            />
            <InputText
              register={register}
              name="email"
              width="300px"
              error={!!errors.email}
              errorMessage={errors?.email?.message}
              label="E-mail"
            />
            <InputText
              register={register}
              name="motherName"
              width="300px"
              error={!!errors.motherName}
              errorMessage={errors?.motherName?.message}
              label="Nome da mãe"
            />
            <InputText
              register={register}
              name="zipCode"
              width="200px"
              error={!!errors.zipCode}
              errorMessage={errors?.zipCode?.message}
              label="CEP"
              setValue={(e) => {
                setZipCode(e);
                setValue("zipCode", cepMask(e), {shouldValidate: true});
              }}
            />
            <InputText
              register={register}
              name="neighborhood"
              width="200px"
              error={!!errors.neighborhood}
              errorMessage={errors?.neighborhood?.message}
              label="Bairro"
            />
            <InputText
              register={register}
              name="street"
              width="300px"
              error={!!errors.street}
              errorMessage={errors?.street?.message}
              label="Rua"
            />
            <InputText
              register={register}
              name="city"
              width="200px"
              error={!!errors.city}
              errorMessage={errors?.city?.message}
              label="Cidade"
            />
            <InputText
              register={register}
              maxLength={2}
              setValue={(e) => setValue("state", String(e).toUpperCase(), {shouldValidate: true})}
              name="state"
              width="150px"
              error={!!errors.state}
              errorMessage={errors?.state?.message}
              label="Estado"
            />
            <InputText
              register={register}
              name="number"
              width="200px"
              error={!!errors.number}
              errorMessage={errors?.number?.message}
              label="Número"
            />
            <InputText
              register={register}
              name="complement"
              width="200px"
              error={!!errors.complement}
              errorMessage={errors?.complement?.message}
              label="Complemento"
            />
          </div>
        </CoverForm>
        <CoverForm color="black" title="Dados de Investimento">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="value"
              width="200px"
              error={!!errors.value}
              errorMessage={errors?.value?.message}
              label="Valor"
              setValue={(e) => setValue("value", valueMask(e || ""), {shouldValidate: true})}
            />
            <InputText
              register={register}
              type="number"
              name="term"
              width="250px"
              setValue={(e) => setValue("term", e, {shouldValidate: true})}
              error={!!errors.term}
              errorMessage={errors?.term?.message}
              label="Prazo investimento - em meses"
              placeholder="ex: 12"
            />
            <InputText
              register={register}
              type="text"
              name="yield"
              width="250px"
              error={!!errors.yield}
              errorMessage={errors?.yield?.message}
              label="Taxa Remuneração Mensal - em %"
              placeholder="ex: 2"
            />
            <div className="w-[200px]">
              <p className="text-white mb-1">Comprar com</p>
              <SelectCustom
                value={watch("purchaseWith")}
                onChange={(e) => setValue("purchaseWith", e.target.value, {shouldValidate: true})}
              >
                <option value={"pix"}>PIX</option>
                <option value={"boleto"}>Boleto</option>
                <option value={"bank_transfer"}>Transferência</option>
              </SelectCustom>
            </div>
            {modalityContract === "SCP" && (
              <>
                <InputText
                  register={register}
                  type="number"
                  name="amountQuotes"
                  width="150px"
                  error={!!errors.amountQuotes}
                  errorMessage={errors?.amountQuotes?.message}
                  label="Quantidade de cotas"
                />
              </>
            )}
            <InputText
              type="date"
              register={register}
              maxDate={moment().format("YYYY-MM-DD")}
              name="initDate"
              width="200px"
              setValue={(e) => setValue("initDate", e, {shouldValidate: true})}
              error={!!errors.initDate}
              errorMessage={errors?.initDate?.message}
              label="Inicio do contrato"
            />
            <InputText
              type="text"
              register={register}
              name="endDate"
              width="200px"
              value={watch("endDate")}
              error={!!errors.endDate}
              errorMessage={errors?.endDate?.message}
              disabled={true}
              label="Final do contrato"
            />
            <div className="w-[200px]">
              <p className="text-white mb-1">Perfil</p>
              <SelectCustom
                value={watch("profile")}
                onChange={(e) => setValue("profile", e.target.value, {shouldValidate: true})}
              >
                <option value={"conservative"}>Conservador</option>
                <option value={"moderate"}>Moderado</option>
                <option value={"aggressive"}>Agressivo</option>
              </SelectCustom>
            </div>
            <div className="w-[100px]">
              <p className="text-white mb-1">Debênture</p>
              <SelectCustom
                value={watch("isDebenture")}
                onChange={(e) => setValue("isDebenture", e.target.value, {shouldValidate: true})}
              >
                <option value={"s"}>Sim</option>
                <option value={"n"}>Não</option>
              </SelectCustom>
            </div>
          </div>
        </CoverForm>

        {userProfile.name !== "broker" && (
          <CoverForm color="black" title="Selecione o broker">
            <div className="flex md:flex-row flex-col w-full gap-4 justify-start">
              <div className="md:w-2/4">
                <SelectSearch
                  label="Broker"
                  items={brokers}
                  value={brokerIdSelected}
                  setValue={setBrokerIdSelected}
                />
              </div>
            </div>
          </CoverForm>
        )}

        {((userProfile.name !== "broker" && brokerIdSelected !== "") ||
          userProfile.name === "broker") && (
          <CoverForm color="black" title="Adicionar Assessor">
            <div>
              {advisors.length > 0 ? (
                advisors?.map((advisor, i) => (
                  <div
                    key={i}
                    className="flex justify-between items-end gap-4 mb-4"
                  >
                    <div className="flex-1">
                      <SelectSearch
                        label="Selecione um Assessor"
                        items={acessors}
                        value={""}
                        setValue={() => {}}
                        loading={loadingAcessors}
                        handleChange={(test) => {
                          const advisorFilter = advisors.filter(
                            (adv) => adv.generatedId === advisor.generatedId
                          )[0];
                          const newAdvisor = {
                            generatedId: advisorFilter.generatedId,
                            id: test.id,
                            taxValue: Number(test.rate),
                          };
                          const newArray = advisors;
                          newArray[i] = newAdvisor;
                          setAdvisors([...newArray]);
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <Input
                        label="Adicione a Taxa - em %"
                        id=""
                        name=""
                        value={String(advisor.taxValue)}
                        type="text"
                        onChange={(e) =>
                          handleInputChange(advisor.generatedId, e.target.value)
                        }
                      />
                    </div>
                    <div
                      className="bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full"
                      onClick={() => {
                        const newArray = advisors.filter(
                          (_, index) => index !== i
                        );
                        setAdvisors(newArray);
                      }}
                    >
                      <TrashIcon width={20} />
                    </div>
                  </div>
                ))
              ) : (
                <div>
                  <p className="text-white">Nenhum assessor adicionado!</p>
                </div>
              )}
              <div
                className="bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5"
                onClick={() => {
                  setAdvisors([
                    ...advisors,
                    {
                      generatedId: crypto.randomUUID(),
                      id: "",
                      taxValue: "",
                    },
                  ]);
                }}
              >
                <PlusIcon width={25} color="#fff" />
              </div>
            </div>
          </CoverForm>
        )}

        <CoverForm color="black" title="Dados bancarios">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="bank"
              width="300px"
              error={!!errors.bank}
              errorMessage={errors?.bank?.message}
              label="Banco"
            />
            <InputText
              register={register}
              name="agency"
              width="200px"
              error={!!errors.agency}
              errorMessage={errors?.agency?.message}
              label="Agência"
            />
            <InputText
              register={register}
              name="accountNumber"
              width="200px"
              error={!!errors.accountNumber}
              errorMessage={errors?.accountNumber?.message}
              label="Conta"
            />
            <InputText
              register={register}
              name="pix"
              width="250px"
              error={!!errors.pix}
              errorMessage={errors?.pix?.message}
              label="Pix"
            />
          </div>
        </CoverForm>
        <CoverForm color="black" title="Anexo de documentos">
          <div>
            <div className="flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white">
              <div>
                <p className="mb-1">Contrato</p>
                <Dropzone onFileUploaded={setContract} />
              </div>
              <div>
                <p className="mb-1">Comprovante</p>
                <div>
                  <Dropzone onFileUploaded={setFile} />
                </div>
              </div>
              <div>
                <p className="mb-1">Documento de identidade</p>
                <div>
                  <Dropzone onFileUploaded={setDocumet} />
                </div>
              </div>
              <div>
                <p className="mb-1">Comprovante de residência</p>
                <div>
                  <Dropzone onFileUploaded={setResidence} />
                </div>
              </div>
            </div>
          </div>
        </CoverForm>
        <div className="md:w-52 mb-10">
          <Button
            label="Enviar"
            size="lg"
            loading={loading}
            disabled={
              loading ||
              !isValid ||
              !contract ||
              !file ||
              !document ||
              !residence
            }
          />
        </div>
      </form>
    </div>
  );
}
