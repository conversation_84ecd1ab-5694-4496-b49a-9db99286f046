import { Screen } from './_screen'

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{
    signatarie?: string;
    page?: string;
    type?: string;
    startData?: string;
    endData?: string;
    status?: string;
  }>;
}) {
  const {
    signatarie = "",
    page = "1",
    type = "all",
    startData = "",
    endData = "",
    status = "Todos",
  } = await searchParams;

  return (
      <Screen
        initialSignatarie={signatarie}
        initialPage={page}
        initialType={type}
        initialStartData={startData}
        initialEndData={endData}
        initialStatus={status}
      />
    );
}