import { AdjustmentsHorizontalIcon, ChevronDownIcon, ChevronUpIcon, XCircleIcon } from "@heroicons/react/24/outline"
import InputSearch from "../InputSearch"
import { useState } from "react"
import Select from "../Select"
import { Button } from "../ui/button"

interface IProps {
  activeModal: boolean
  setActiveModal: (active: boolean) => void
  inputPlaceholder?: string
  filterData?: {
    startData: string
    endData?: string
    input?: string
    type?: string
    filterOptions?: {
      label: string
      value: string
    }[]
  }
  children?: React.ReactNode
  setFilterData?: (data: any) => void
  handleSearch: () => void
}

export default function FilterModal({activeModal, setActiveModal, filterData, setFilterData, handleSearch, inputPlaceholder = "Pesquisar", children}: IProps) {
  return (
    <div className='flex flex-col md:flex-row md:items-center items-end relative gap-5 justify-end'>
      {
        filterData?.input !== undefined && (
          <div className='md:w-80 mb-2 md:mb-0'>
            <InputSearch
              handleSearch={() => {
                handleSearch()
              }}
              placeholder={inputPlaceholder}
              setValue={(e) => {
                if(setFilterData) {
                  setFilterData({
                    ...filterData,
                    input: e
                  })
                }
              }}
              value={filterData.input}
            />
          </div>
      )
      }
      <div className='flex w-24 md:items-center p-2 rounded-lg bg-[#3A3A3A] cursor-pointer' onClick={(() => setActiveModal(!activeModal))}>
        <AdjustmentsHorizontalIcon width={15} color='#fff' />
        <p className='px-2 text-sm'>Filtros</p>
        {
          activeModal ? <ChevronUpIcon width={15} color='#fff' /> : <ChevronDownIcon width={15} color='#fff' />
        }
      </div>
      {
        activeModal && (
          <div className='absolute md:w-[300px] w-[250px] bg-[#3A3A3A] p-5 top-10 rounded-tl-lg rounded-b-lg z-10'>
            <div className='flex w-full justify-between items-center'>
              <p className='text-base'>Filtros</p>
              <div className='cursor-pointer' onClick={() => setActiveModal(false)}>
                <XCircleIcon  width={20}/>
              </div>
            </div>
            {
              children ? <div className="mt-5">{children}</div> : (
                <div>
                  <div className='flex md:flex-row flex-col justify-between mt-5'>
                    <div className='mb-2 md:mb-0'>
                      <p className='text-xs'>Início</p>
                      <input 
                        value={filterData?.startData} 
                        className='p-1 rounded-md text-xs bg-transparent border' 
                        onChange={({target}) => {
                          if(setFilterData) {
                            setFilterData({
                              ...filterData,
                              startData: target.value
                            })
                          }
                        }} 
                        type="date" 
                      />
                    </div>
                    {
                      filterData?.endData !== undefined && (
                        <div className=''>
                          <p className='text-xs'>Fim</p>
                          <input
                            value={filterData.endData} 
                            className='p-1 rounded-md text-xs bg-transparent border' 
                            onChange={({target}) => {
                              if(setFilterData) {
                                setFilterData({
                                  ...filterData,
                                  endData: target.value
                                })
                              }
                            }} 
                            type="date" 
                          />
                        </div>
                    )
                    }
                  </div>
                  <div className='w-full mt-3'>
                    {
                      filterData?.filterOptions && (
                        <Select
                          options={filterData.filterOptions}
                          selected={filterData.type || ''}
                          setSelected={(d: string) => {
                            if(setFilterData) {
                              setFilterData({
                                ...filterData,
                                type: d
                              })
                            }
                          }}
                        />
                      )
                    }
                  </div>
                </div>
              )
            }
            <div className='m-auto mt-5'>
              <Button
                onClick={() => {
                  handleSearch()
                  setActiveModal(false)
                }}
                className="w-full"
              >
                Aplicar
              </Button>
            </div>
          </div>
        )
      }
    </div>
  )
}