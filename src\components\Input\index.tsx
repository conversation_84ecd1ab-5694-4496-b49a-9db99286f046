import { InputHTMLAttributes, forwardRef, useState } from "react"
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline"

interface Iprops extends InputHTMLAttributes<HTMLInputElement> {
  label: string
  bg?: 'transparent' | 'fill'
}

const Input = forwardRef<HTMLInputElement, Iprops>(({ label, bg, type, ...props }, ref) => {
  const [activePassword, setActivePassword] = useState(false)

  return (
    <div>
      <p className="text-white mb-1">{label}</p>
      <div className={`custom-input-wrapper h-12 w-full text-white rounded-xl ring-[#FF9900] ring-1 ${type === 'month' ? '' : 'ring-inset'} ${bg === 'transparent' ? 'bg-black' : 'bg-[#1C1C1C]'} flex-1 flex relative`}>
        <input
          ref={ref}
          type={activePassword && type === 'password' ? 'text' : type}
          {...props}
          className="w-full h-12 flex-1 px-4 bg-transparent rounded-xl"
        />
        {type === 'password' && (
          <div
            className="mr-2 cursor-pointer absolute right-0 top-[50%] translate-y-[-50%]"
            onClick={() => setActivePassword(!activePassword)}
          >
            {activePassword ? <EyeSlashIcon width={20} /> : <EyeIcon width={20} />}
          </div>
        )}
      </div>
    </div>
  )
})

Input.displayName = 'Input'
export default Input