import Button from "@/components/Button/Button";
import Dropzone from "@/components/Dropzone";
import Input from "@/components/Input";
import { signIca } from "@/constants/signIca";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import Contact from "@/models/contract";
import formatNumberValue from "@/utils/formatNumberValue";
import { clearLetters, cpfMask, valueMask } from "@/utils/masks";
import moment from "moment-timezone";
import { useState } from "react";
import { toast } from "react-toastify";

interface IProps {
  contract: Contact
  setOpenModal: (d: any) => void
}

export default function AditiveContract({ contract, setOpenModal }: IProps) {
  const [payment, setPayment] = useState<FileList>()
  const [newContract, setNewContract] = useState<FileList>()
  const [type, setType] = useState<'new' | 'exist'>('new')
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState({
    value: '',
    profile: '',
    yield: '',
    date: '',
    bank: '',
    agency: '',
    accountNumber: '',
    pix: '',
    comment: '',
    ownerName: '',
    ownerCpf: ''
  })

  const onSubmit = () => {
    if(type === 'new') {
      createAditive()
    } else {
      registerAditiveExists()
    }
  }

  const createAditive = () => {
    setLoading(true)
    const payload = {
      contractId: contract?.idContrato,
      investment: {
        value: formatNumberValue(data.value),
        profile: data.profile,
        yield: Number(contract.rendimentoInvestimento),
        date: moment(data.date).format('YYYY-MM-DD')
      },
      accountBank: {
        bank: data.bank,
        accountNumber: data.accountNumber,
        agency: data.agency,
        pix: data.pix
      },
      owner: contract.documentoInvestidor.length > 11 ? {
        name: data.ownerName,
        cpf: clearLetters(data.ownerCpf)
      } : undefined,
      observations: data.comment,
      signIca: signIca
    }

    api.post('/contract/additive', payload).then(resp => {
      toast.success('Contrato de aditivo criado com sucesso!')
      setOpenModal(false)
    }).catch(error => {
      returnError(error.message, 'Não conseguimos criar o contrato de aditivo!')
    }).finally(() => setLoading(false))
  }

  const registerAditiveExists = () => {
    if((type === 'exist') && (!newContract || !payment)) {
      return toast.error('É necessário anexar o contrato e o comprovante de pagamento')
    }

    setLoading(true)
    const form = new FormData()

    if(newContract) {
      form.append('contractPdf', newContract[0])
    }

    if(payment) {
      form.append('proofPayment', payment[0])
    }

    form.append('investment[yield]', data.yield !== '' || Number(data.yield) > 0 ? data.yield : contract.rendimentoInvestimento)

    form.append('contractId', contract?.idContrato)
    form.append('investment[value]', data.value.replace('.', '').replace(',', '.'))
    form.append('investment[date]', moment(data.date).format('YYYY-MM-DD'))
    form.append('accountBank[bank]', data.bank)
    form.append('accountBank[accountNumber]', data.accountNumber)
    form.append('accountBank[agency]', data.agency)
    form.append('accountBank[pix]', data.pix)
    form.append('observations', data.comment)

    api.post('/contract/additive-manual', form).then(resp => {
      toast.success('Contrato de aditivo cadastrado com sucesso!')
      setOpenModal(false)
    }).catch(err => {
      toast.error(err?.response?.data?.message || 'Não foi possível cadastrar o aditivo')
    }).finally(() => setLoading(false))
  }

  return (
    <div className="fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-20">
      <div className="md:w-6/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900] overflow-auto h-[88%]">
        <p className="text-2xl font-bold">Criar contrato aditivo</p>

        <div className="mt-5 w-full">
          <div className="mb-5">
            <div className="flex gap-4 mb-5">
              <div>
                <input type="checkbox" name="" checked={type === 'new'} onChange={() => setType('new')} id="novo" className="mr-2 cursor-pointer" />
                <label htmlFor="novo" className="cursor-pointer select-none">Criar novo aditivo</label>
              </div>
              <div>
                <input type="checkbox" name="" checked={type === 'exist'} onChange={() => setType('exist')} id="manual" className="mr-2 cursor-pointer" />
                <label htmlFor="manual" className="cursor-pointer select-none">Cadastrar aditivo existente</label>
              </div>
            </div>
            {
                type === 'new' && contract.documentoInvestidor.length > 11 && (
                  <>
                    <p className="mb-3 text-xl">Dados do Representante Legal</p>
                    <div className="flex flex-wrap gap-4 mb-5">
                      <Input
                        id=""
                        label="Nome"
                        name=""
                        type="text"
                        value={data.ownerName}
                        onChange={(e) => {
                          setData({
                            ...data,
                            ownerName: e.target.value
                          })
                        }}
                      />
                      <Input
                        id=""
                        label="CPF"
                        name=""
                        type="text"
                        value={data.ownerCpf}
                        onChange={(e) => {
                          setData({
                            ...data,
                            ownerCpf: cpfMask(e.target.value)
                          })
                        }}
                      />
                    </div>
                  </>
                )
              }

            <p className="mb-3 text-xl">Dados de Investimento</p>
            
            <div className="flex flex-wrap gap-4">
              <Input
                id=""
                label="Valor do investimento"
                name=""
                type="text"
                value={data.value}
                onChange={(e) => {
                  setData({
                    ...data,
                    value: valueMask(e.target.value)
                  })
                }}
              />
              <Input
                id=""
                label="Perfil investidor"
                name=""
                type="text"
                value={data.profile}
                onChange={(e) => {
                  setData({
                    ...data,
                    profile: e.target.value
                  })
                }}
              />
              <Input
                id=""
                label="Data da aplicação"
                name=""
                type="date"
                value={data.date}
                min={type !== 'exist' ? moment.utc().format('YYYY-MM-DD') : undefined}
                max={type === 'exist' ? moment.utc().format('YYYY-MM-DD') : undefined}
                onChange={(e) => {
                  setData({
                    ...data,
                    date: e.target.value
                  })
                }}
              />
            </div>
          </div>
          <div className="mb-10">
            <p className="mb-3 text-xl">Dados bancarios</p>
            <div className="flex flex-wrap gap-4">
              <Input
                id=""
                label="Nome do banco"
                name=""
                type="text"
                value={data.bank}
                onChange={(e) => {
                  setData({
                    ...data,
                    bank: e.target.value
                  })
                }}
              />
              <Input
                id=""
                label="Conta"
                name=""
                type="text"
                value={data.accountNumber}
                onChange={(e) => {
                  setData({
                    ...data,
                    accountNumber: e.target.value
                  })
                }}
              />
              <Input
                id=""
                label="Agência"
                name=""
                type="text"
                value={data.agency}
                onChange={(e) => {
                  setData({
                    ...data,
                    agency: e.target.value
                  })
                }}
              />
              <Input
                id=""
                label="Chave pix"
                name=""
                type="text"
                value={data.pix}
                onChange={(e) => {
                  setData({
                    ...data,
                    pix: e.target.value
                  })
                }}
              />
              
            </div>
          </div>
          <div className="w-10/12">
            <p>Observações</p>
            <textarea
              value={data.comment}
              onChange={({target}) => setData({
                ...data,
                comment: target.value
              })}
              className="w-full text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 p-2 mt-2"
            />
          </div>
        </div>
        {
          type === 'exist' && (
            <div className='md:flex-row flex flex-col gap-2 mb-10 text-white jus  mt-5'>
              <div>
                <p className='mb-1'>Aditivo</p>
                <Dropzone onFileUploaded={setNewContract} />
              </div>
              <div>
                <p className='mb-1'>Comprovante de pagamento</p>
                <Dropzone onFileUploaded={setPayment} />
              </div>
            </div>
          )
        }

        <div className='flex mt-10 gap-4 justify-end'>
          <div >
            <Button 
              label="Criar aditivo"
              loading={loading}
              className="bg-orange-linear"
              disabled={loading}
              handleSubmit={onSubmit}
            />
          </div>
          {/* <div className='bg-[#D34653] px-4 flex items-center justify-center cursor-pointer'>
            <p className='text-sm'>Cancelar Contrato</p>
          </div> */}
          <div >
            <Button 
              label="Fechar"
              loading={false}
              handleSubmit={() => setOpenModal(false)}
            />
          </div>
        </div>
      </div>
    </div>
  )
}