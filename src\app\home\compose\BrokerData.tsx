import Select from "@/components/Select";
import Skeleton from "@/components/Skeleton/intex";
import api from "@/core/api";
import User, { UserProfile } from "@/models/user";
import customTheme from "@/styles/chartTheme";
import { UsersIcon } from "@heroicons/react/16/solid";
import { BanknotesIcon, BuildingOfficeIcon, ChartBarIcon, ChartBarSquareIcon, DocumentTextIcon } from "@heroicons/react/24/outline";
import { AgChartOptions } from "ag-charts-community";
import { AgCharts } from "ag-charts-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import DocIcon from '@/assets/Icons/doc_verify.svg'
import PersonsIcon from '@/assets/Icons/grafic_persons.svg'
import moment from "moment";
import returnError from "@/functions/returnError";
import daysOfWeek from "@/utils/daysOfWeek";
import { getUser, getUserProfile } from "@/functions/getUserData";

interface IProps {
  setModal: (d: any) => void
}

type MonthlyData = {
  month: string;
  totalContracts: number;
  totalValue: string | number;
  date: string;
  label: string;
};

type FormattedData = {
  month: string;
  value: number;
};

interface IData {
  distributedIncome: number,
  scpWithdraws: number,
  p2pWithdraws: number,
  p2pContractNumber: number,
  scpContractNumber: number,
  p2pContractAmount: number,
  scpContractAmount: number,
  activeQuotes: number,
  shareholder: number,
  activeInvestorsNumber: number
}

export default function BrokerData({setModal}: IProps) {
  const [data, setData] = useState<IData>()
  const [loading, setLoading] = useState(true)
  const user = getUser()
  const userProfile = getUserProfile()
  const userName = user.name.split(' ')

  const [filterContracts, setFilterContracts] = useState({
    type: 'SCP',
    period: 'year'
  })
  const [filterResults, setFilterResults] = useState({
    type: 'SCP',
    period: 'year'
  })

  const [chartOptions, setChartOptions] = useState<AgChartOptions>({
    data: [
        { month: 'Jan', avgTemp: 2.3, value: 0, name: 'Janeiro' },
        { month: 'Fev', avgTemp: 2.3, value: 0, name: 'Janeiro' },
        { month: 'Mar', avgTemp: 6.3, value: 0, name: 'Janeiro' },
        { month: 'Abr', avgTemp: 16.2, value: 0, name: 'Janeiro' },
        { month: 'Mai', avgTemp: 22.8, value: 0, name: 'Janeiro' },
        { month: 'Jun', avgTemp: 14.5, value: 0, name: 'Janeiro' },
        { month: 'Jul', avgTemp: 8.9, value: 0, name: 'Janeiro' },
        { month: 'Ago', avgTemp: 8.9, value: 0, name: 'Janeiro' },
        { month: 'Set', avgTemp: 8.9, value: 0, name: 'Janeiro' },
        { month: 'Out', avgTemp: 8.9, value: 0, name: 'Janeiro' },
        { month: 'Nov', avgTemp: 8.9, value: 0, name: 'Janeiro' },
        { month: 'Dez', avgTemp: 8.9, value: 0, name: 'Janeiro' },
    ],
    background: {
      visible: false
    },
    series: [
    // @ts-ignore
    { type: 'bar', xKey: 'month', yKey: 'value', tooltip: {
      enabled: false
    }, 
    label: {
      enabled: false,
    },
    }
  ],
    axes: [
      {
      // @ts-ignore
        type: 'category',
        position: 'bottom',
        key: 'month'
      },
      {
        // @ts-ignore
        type: 'number',
        position: 'left',
        label: {
          formatter: (params) => {
            return params.value.toLocaleString('pt-br', {
              style: 'currency',
              currency: 'BRL'
            })
          }
        }
      }
    ],
    padding: {
      top: 10
    },
    theme: customTheme,
  });

  const capitalizeFirstLetter = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const returnTitle = (text: string, filter: string) => {
    if(filter === 'year') {
      return capitalizeFirstLetter(moment(text).utc().format("MMM"))
    } else if (filter === 'month') {
      return text
    } else {
      return daysOfWeek[text as keyof typeof daysOfWeek]
    }
  }

  const formatData = (data: MonthlyData[], type: 'contract' | 'results', filter: string): FormattedData[] => {
    return data.map((item) => {
      return {
        month: filter === 'year' ? returnTitle(item.date, filter) : returnTitle(item.label, filter),
        value: Number(type === 'contract'? item.totalContracts : item.totalValue)
      }
    });
  };

  const getChartContractsData = () => {
    api.get(`${userProfile.name}/${userProfile.name !== 'superadmin' ? `${userProfile.roleId}/` : ''}contracts-growth`, {
      params: {
        period: filterContracts.period,
        contractType: filterContracts.type
      }
    }).then(resp => {
      const formattedData = formatData(resp.data.data, 'results', filterContracts.period)
      setChartOptions({
        ...chartOptions,
        data: [...formattedData]
      })
    }).catch(err => {
      returnError(err, 'Erro ao buscar dados do gráfico de contratos!')
    })
  }

  useEffect(() => {
    getChartContractsData()
  }, [filterContracts])

  useEffect(() => {
    getChartResultsData()
  }, [filterResults])

  const getChartResultsData = () => {
    api.get(`${userProfile.name}/${userProfile.name !== 'superadmin' ? `${userProfile.roleId}/` : ''}contracts-growth`, {
      params: {
        period: filterResults.period,
        contractType: filterResults.type
      }
    }).then(resp => {
      const formattedData = formatData(resp.data.data, 'contract', filterResults.period)
      setOptions({
        ...options,
        data: [...formattedData]
      })
    }).catch(err => {
      returnError(err, 'Erro ao buscar dados do gráfico de resultados!')
    })
  }


  const [options, setOptions] = useState<AgChartOptions>({
    background: {
      visible: false
    },
    theme: customTheme,
    data: [
      { month: "Jan", value: 0 },
      { month: "Fev", value: 0 },
      { month: "Mar", value: 0 },
      { month: "Abr", value: 0 },
      { month: "Mai", value: 0 },
      { month: "Jun", value: 0 },
      { month: "Jul", value: 0 },
      { month: "Ago", value: 0 },
      { month: "Set", value: 0 },
      { month: "Out", value: 0 },
      { month: "Nov", value: 0 },
      { month: "Dez", value: 0 },
    ],
    series: [
      {
        type: "line",
        xKey: "month",
        xName: "Month",
        yKey: "value",
        interpolation: { type: "linear" },
        tooltip: {
          enabled: false
        }
      },
    ],
  });

  function getAdvisorData () {
    setLoading(true)
    api.get(userProfile.name === 'broker' ? '/broker/dashboard' : '/advisor/dashboard').then((resp) => {
      setData(resp.data)
    }).catch(err => {

    }).finally(() => setLoading(false))
  }

  useEffect(() => {
    getAdvisorData()
  }, [])
  
  return (
    <div className="w-full">
      <div className='w-full flex md:flex-row flex-col gap-2 justify-between'>
        <div className='bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border'>
          <div className='flex w-full'>
            <div className='w-6 h-6 p-1 bg-white rounded-full flex items-center justify-center'>
              <p className='text-[#FF9900] text-xs font-bold'>
                {`${userName[0][0] || ''}${userName[userName.length - 1][0] || ''}`}
              </p>
            </div>
            <div className="w-full">
              <p className='ml-3 text-sm'>{user?.name}</p>
              <div className="w-full flex justify-end">
                {/* <div className='bg-orange-linear text-center rounded-md mt-3 py-1 px-5 cursor-pointer' onClick={() => setModal(true)}>
                  <p className='text-sm'>Link de pré-cadastro</p>
                </div> */}
              </div>
            </div>
          </div>
        </div>
        <div className='bg-[#1C1C1C] md:w-[60%] p-5 rounded-lg border-[#FF9900] border'>
          <div className='flex-col w-full'>
            <div className=''>
              <BuildingOfficeIcon width={20} color='#FF9900' />
            </div>
            <div className='mt-1'>
              <p className='text-sm'>FAQ ICA BANK SOLUÇÕES FINANCEIRAS LTDA</p>
              <div>
                <p className='text-xs'>Tire suas dúvidas em nossa base de conhecimento</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-wrap gap-2 mt-2">
      <div className='bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border'>
        <div className='flex'>
          <div className='w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center'>
            <DocumentTextIcon width={15} color='#fff' /> 
          </div>
          <p className='ml-3 text-sm'>Investidores Ativos</p>
        </div>
        <div className='ml-5'>
          <div className='mt-5'>
            <p className='text-gradient font-bold text-xs'>SCP</p>
            <Skeleton loading={loading} height="25px">
              <p className='font-bold text-lg'>{data?.scpContractNumber}</p>
            </Skeleton>
          </div>
          <div className='mt-1'>
            <p className='text-gradient font-bold text-xs'>Mútuo</p>
            <Skeleton loading={loading} height="25px">
              <p className='font-bold text-lg'>{data?.p2pContractNumber}</p>
            </Skeleton>
          </div>
        </div>
      </div>
      <div className='bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border'>
        <div className='flex'>
          <div className='w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center'>
            <BanknotesIcon width={15} color='#fff' /> 
          </div>
          <p className='ml-3 text-sm'>Total de retiradas</p>
        </div>
        <div className='ml-5'>
          <div className='mt-5'>
            <p className='text-gradient font-bold text-xs'>Mútuo</p>
            <Skeleton loading={loading} height="25px">
              <p className='font-bold text-lg'>{Number(data?.p2pWithdraws || 0)}</p>
            </Skeleton>
          </div>
          <div className='mt-1'>
            <p className='text-gradient font-bold text-xs'>Saldo bloqueado</p>
            <Skeleton loading={loading} height="25px">
              <p className='font-bold text-lg'>{Number(data?.scpWithdraws || 0)}</p>
            </Skeleton>
          </div>
        </div>
      </div>
      <div className='w-full flex gap-2 justify-between flex-wrap'>
        <div className='bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border'>
          <div className='flex'>
            <div className='w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center'>
              <UsersIcon width={15} color='#fff' /> 
            </div>
            <p className='ml-3 text-sm'>Total de contratos ativos</p>
          </div>
          <div className='w-full text-center mt-3'>
            <Skeleton loading={loading} height="25px">
              <p className='font-bold text-3xl'>{data?.activeInvestorsNumber}</p>
            </Skeleton>
          </div>
        </div>
        <div className='bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border'>
          <div className='flex'>
            <div className='w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center'>
              <UsersIcon width={15} color='#fff' /> 
            </div>
            <p className='ml-3 text-sm'>Valor total de contratos</p>
          </div>
          <div className='w-full text-center mt-3'>
            <Skeleton loading={loading} height="25px">
              <p className='font-bold text-xl'>{(Number(data?.p2pContractAmount) + Number(data?.scpContractAmount))?.toLocaleString('pt-br', {
                style: 'currency',
                currency: 'BRL'
              })}</p>
            </Skeleton>
          </div>
        </div>
        <div className='bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border'>
          <div className='flex'>
            <div className='w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center'>
              <BanknotesIcon width={15} color='#fff' /> 
            </div>
            <p className='ml-3 text-sm'>Cotas ativas</p>
          </div>
          <div className='w-full text-center mt-3'>
            <Skeleton loading={loading} height="25px">
              <p className='font-bold text-xl'>{data?.activeQuotes}</p>
            </Skeleton>
          </div>
        </div>
      </div>
      <div className='w-full flex gap-2 justify-between flex-wrap'>
        <div className='bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border'>
          <div className='flex mb-5 justify-between'>
            <div>
              <Image src={DocIcon} alt="" width={25} style={{marginTop: '-5px'}} />
              <p className='text-sm mt-2'>Gráfico Crescente de Contratos</p>
            </div>
            <div className="flex justify-end gap-2">
              <Select 
                align="center"
                options={[
                  {
                    label: 'SCP',
                    value: "SCP"
                  },
                  {
                    label: 'Mútuo',
                    value: "P2P"
                  },
                ]}
                size="small"
                selected={filterContracts.type}
                setSelected={(e) => {
                  setFilterContracts({
                    ...filterContracts,
                    type: e
                  })
                }}
              />
              <Select
                align="center"
                options={[
                  {
                    label: 'Esse ano',
                    value: "year"
                  },
                  {
                    label: 'Esse mês',
                    value: "month"
                  },
                  {
                    label: 'Essa semana',
                    value: "week"
                  },
                ]}
                size="small"
                selected={filterContracts.period}
                setSelected={(e) => {
                  setFilterContracts({
                    ...filterContracts,
                    period: e
                  })
                }}
              />
            </div>
          </div>
          <AgCharts className="p-2" options={chartOptions} />
        </div>
        <div className='bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border'>
          <div className='flex mb-5 justify-between'>
            <div>
              <Image src={PersonsIcon} alt="" width={25} style={{marginTop: '-5px'}} />
              <p className='text-sm'>Gráfico Crescente de Resultados</p>
            </div>
            <div className="flex justify-end gap-2">
              <Select 
                align="center"
                options={[
                  {
                    label: 'SCP',
                    value: "SCP"
                  },
                  {
                    label: 'Mútuo',
                    value: "P2P"
                  },
                ]}
                size="small"
                selected={filterResults.type}
                setSelected={(e) => {
                  setFilterResults({
                    ...filterResults,
                    type: e
                  })
                }}
              />
              <Select 
                align="center"
                options={[
                  {
                    label: 'Esse ano',
                    value: "year"
                  },
                  {
                    label: 'Esse mês',
                    value: "month"
                  },
                  {
                    label: 'Essa semana',
                    value: "week"
                  },
                ]}
                size="small"
                selected={filterResults.period}
                setSelected={(e) => {
                  setFilterResults({
                    ...filterResults,
                    period: e
                  })
                }}
              />
            </div>
          </div>
          <AgCharts options={options} />
        </div>
      </div>
    </div>
    </div>
  )
}