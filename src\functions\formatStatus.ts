export enum ContractStatus {
  EXPIRED_BY_AUDIT = 'EXPIRED_BY_AUDIT', // EXPIRADO POR NÃO ASSINATURA DA AUDITORIA
  EXPIRED_FAILURE_PROOF_PAYMENT = 'EXPIRED_FAILURE_PROOF_PAYMENT', // EXPIRADO POR NÃO INSERIR COMPROVANTE
  DRAFT = 'DRAFT', // RASCUNHO
  GENERATED = 'GENERATED', // CONTRATO GERADO RASCUNHO
  SIGNATURE_SENT = 'SIGNATURE_SENT', // ENVIO DA ASSINATURA
  AWAITING_INVESTOR_SIGNATURE = 'AWAITING_INVESTOR_SIGNATURE', // AGUARDANDO ASSINATURA DO INVESTIDOR
  AWAITING_DEPOSIT = 'AWAITING_DEPOSIT', // AGUARDANDO COMPROVANTE DE PAGAMENTO
  AWAITING_AUDIT = 'AWAITING_AUDIT', // AGUARDANDO AUDITORIA
  AWAITING_AUDIT_SIGNATURE = 'AWAITING_AUDIT_SIGNATURE', // AGUARDANDO ASSINATURA DA AUDITORIA
  ACTIVE = 'ACTIVE', // ATIVO
  SIGNATURE_FAILED = 'SIGNATURE_FAILED', // FALHA AO ENVIAR CONTRATO PARA A ASSINATURA
  EXPIRED_BY_INVESTOR = 'EXPIRED_BY_INVESTOR', // EXPIRADO POR FALTA DE ASSINATURA DO CONTRATO
  REJECTED = 'REJECTED', // REJEITADO
  REJECTED_BY_AUDIT = 'REJECTED_BY_AUDIT', // REJEITADO
  GENERATE_CONTRACT_FAILED = 'GENERATE_CONTRACT_FAILED', // FALHA AO GERAR CONTRATO
  EXPIRED = 'EXPIRED', // EXPIRADO
}

type ContractStatusFormatted = {
  title: string
  description: string
  textColor: string
}

export function formatStatusContract(
  status: ContractStatus,
): ContractStatusFormatted {
  const statusMap: Record<ContractStatus, ContractStatusFormatted> = {
    [ContractStatus.DRAFT]: {
      title: 'Rascunho',
      description: 'Contrato ainda em fase de edição',
      textColor: 'text-[#FF9900]',
    },
    [ContractStatus.GENERATED]: {
      title: 'Gerado',
      description: 'Contrato gerado com sucesso e aguardando ação',
      textColor: 'text-[#FF9900]',
    },
    [ContractStatus.SIGNATURE_SENT]: {
      title: 'Assinatura',
      description: 'Contrato enviado para assinatura digital',
      textColor: 'text-[#FF9900]',
    },
    [ContractStatus.AWAITING_INVESTOR_SIGNATURE]: {
      title: 'Aguardando',
      description: 'Aguardando que o investidor assine o contrato',
      textColor: 'text-[#FF9900]',
    },
    [ContractStatus.AWAITING_DEPOSIT]: {
      title: 'Aguardando',
      description: 'Aguardando envio do comprovante de pagamento',
      textColor: 'text-[#FF9900]',
    },
    [ContractStatus.AWAITING_AUDIT]: {
      title: 'Aguardando',
      description: 'Contrato aguardando análise da auditoria',
      textColor: 'text-[#FF9900]',
    },
    [ContractStatus.AWAITING_AUDIT_SIGNATURE]: {
      title: 'Aguardando',
      description: 'Aguardando assinatura final da auditoria',
      textColor: 'text-[#FF9900]',
    },
    [ContractStatus.ACTIVE]: {
      title: 'Ativo',
      description: 'Contrato ativo e em vigor',
      textColor: 'text-[#3cff00]',
    },
    [ContractStatus.SIGNATURE_FAILED]: {
      title: 'Falha',
      description: 'Erro ao tentar enviar o contrato para assinatura',
      textColor: 'text-red-500',
    },
    [ContractStatus.EXPIRED_BY_INVESTOR]: {
      title: 'Expirado',
      description: 'Contrato expirou por falta de assinatura do investidor',
      textColor: 'text-red-500',
    },
    [ContractStatus.EXPIRED_BY_AUDIT]: {
      title: 'Expirado',
      description: 'Contrato expirou por não ter sido assinado pela auditoria',
      textColor: 'text-red-500',
    },
    [ContractStatus.EXPIRED_FAILURE_PROOF_PAYMENT]: {
      title: 'Expirado',
      description:
        'Contrato expirado por não envio do comprovante de pagamento',
      textColor: 'text-red-500',
    },
    [ContractStatus.REJECTED_BY_AUDIT]: {
      title: 'Rejeitado',
      description: 'Contrato foi rejeitado pela auditoria.',
      textColor: 'text-red-500',
    },
    [ContractStatus.REJECTED]: {
      title: 'Rejeitado',
      description: 'Contrato foi rejeitado por alguma das partes',
      textColor: 'text-red-500',
    },
    [ContractStatus.GENERATE_CONTRACT_FAILED]: {
      title: 'Erro',
      description: 'Ocorreu uma falha ao gerar o contrato',
      textColor: 'text-red-500',
    },
    [ContractStatus.EXPIRED]: {
      title: 'Expirado',
      description: 'Contrato expirado por tempo excedido ou inatividade',
      textColor: 'text-red-500',
    },
  }

  // Validação para status desconhecido
  if (!(status in statusMap)) {
    return {
      title: 'Desconhecido',
      description: 'Status não reconhecido pelo sistema',
      textColor: 'text-gray-300',
    }
  }

  return statusMap[status]
}

export enum AddendumStatus {
  DRAFT = 'DRAFT', // Rascunho: contrato criado, mas ainda não enviado para assinatura
  SENT_FOR_SIGNATURE = 'SENT_FOR_SIGNATURE', // Contrato enviado para assinatura das partes
  PENDING_INVESTOR_SIGNATURE = 'PENDING_INVESTOR_SIGNATURE', // Aguardando assinatura do investidor
  FULLY_SIGNED = 'FULLY_SIGNED', // Todas as partes assinaram o contrato
  CANCELED = 'CANCELED', // Contrato cancelado antes de ser concluído
  EXPIRED = 'EXPIRED', // Contrato expirado sem ser assinado dentro do prazo
}

export function formatStatusAditive(status: string) {
  switch (status) {
    case 'DRAFT':
      return {
        title: 'Erro',
        textColor: 'text-[#FF0000]',
        description: 'Ocorreu um erro na confecção do contrato',
      }
    case 'SENT_FOR_SIGNATURE':
      return {
        title: 'Aguardando',
        textColor: 'text-[#FF9900]',
        description: 'Contrato enviado para assinatura das partes',
      }
    case 'PENDING_INVESTOR_SIGNATURE':
      return {
        title: 'Aguardando',
        textColor: 'text-[#FF9900]',
        description: 'Aguardando assinatura do investidor',
      }
    case 'FULLY_SIGNED':
      return {
        title: 'Finalizado',
        textColor: 'text-[#3cff00]',
        description: 'Todas as partes assinaram o contrato',
      }
    case 'CANCELED':
      return {
        title: 'Cancelado',
        textColor: 'text-[#FF0000]',
        description: 'Contrato cancelado antes de ser concluído',
      }
    case 'EXPIRED':
      return {
        title: 'Expirado',
        textColor: 'text-[#FF0000]',
        description: 'Contrato expirado sem ser assinado dentro do prazo',
      }
    default:
      return {
        title: 'Processando',
        textColor: 'text-[#FF9900]',
        description: '',
      }
  }
}

export function formatStatusIncomePayment(
  status: 'PENDENT' | 'SEND' | 'ERROR' | 'DELIVERED',
) {
  switch (status) {
    case 'PENDENT':
      return {
        title: 'Pendente',
        textColor: 'text-[#FFB238]',
        description: 'Informe de rendimentos pendente para enviar',
        borderColor: 'border-[#FFB238]',
        backgroundColor: 'bg-[#FFB238]',
      }
    case 'DELIVERED':
      return {
        title: 'Enviando',
        textColor: 'text-[#429AEC]',
        description: 'Envio em andamento ao destinatário',
        backgroundColor: 'bg-[#429AEC]',
        borderColor: 'border-[#429AEC]',
      }
    case 'SEND':
      return {
        title: 'Enviado',
        textColor: 'text-[#1EF97C]',
        description: 'Informe de rendimentos enviado com sucesso!',
        backgroundColor: 'bg-[#1EF97C]',
        borderColor: 'border-[#1EF97C]',
      }
    case 'ERROR':
      return {
        title: 'Erro',
        textColor: 'text-[#F10303]',
        description: 'Erro ao enviar o informe de rendimentos',
        borderColor: 'border-[#F10303]',
        backgroundColor: 'bg-[#F10303]',
      }
  }
}
