import { UseFormRegister } from "react-hook-form";

interface IProps {
  options: {
    label: string;
    value: string;
  }[];
  optionSelected?: string;
  setOptionSelected?: (d: any) => void;
  label: string;
  placeHolder?: string;
  width?: string;
  register?: UseFormRegister<any> | any;
  error?: boolean;
  errorMessage?: string;
  name?: string;
  disableErrorMessage?: boolean;
  disabled?: boolean;
}

export default function InputSelect({
  optionSelected,
  options,
  setOptionSelected,
  label,
  placeHolder = "",
  width = "100%",
  register = () => {},
  error,
  errorMessage,
  name = "",
  disableErrorMessage = false,
  disabled = false,
}: IProps) {
  return (
    <div className="inputSelect relative group" style={{ width }}>
      <p className="text-white mb-1 text-sm">{label}</p>

      <select
        disabled={disableErrorMessage && !errorMessage}
        {...register(name)}
        value={optionSelected}
        onChange={({ target }) => {
          if (setOptionSelected) {
            setOptionSelected(target.value);
          }
        }}
        className={`h-12 w-full px-4 ${
          disabled ? "text-zinc-400" : "text-white"
        } rounded-xl ${
          error ? "ring-[#f33636]" : "ring-[#FF9900]"
        } ring-1 ring-inset bg-black flex-1`}
      >
        {placeHolder && (
          <option selected disabled value="">
            {placeHolder}
          </option>
        )}
        {options.map((option, index) => (
          <option key={index} className="cursor-pointer" value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && (
        <div className=" absolute gr max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[90%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block">
          {errorMessage}
        </div>
      )}
    </div>
  );
}
