import { useEffect } from "react"
import { UseFormRegister } from "react-hook-form"

import './style.css'

interface IProps {
  label: string
  setValue?: (d: any) => void
  error?: boolean
  errorMessage?: string
  width?: string
  register: UseFormRegister<any>
  name: string
  placeholder?: string
  type?: 'text' | 'date' | 'number'
  disabled?: boolean
  minDate?: string
  minLength?: number
  maxLength?: number
  maxDate?: string
  disableErrorMessage?: boolean
  onBlur?: () => void
}

export default function InputText({
  label,
  setValue,
  error,
  errorMessage,
  width='auto',
  register,
  name,
  placeholder='',
  type='text',
  disabled = false,
  minDate,
  minLength,
  maxLength,
  maxDate,
  disableErrorMessage = false,
  onBlur
}: IProps) {
  useEffect(() => {

  }, [width])

  return (
    <div className="input" style={{width}}>
      <p className="text-white mb-1">{label} {error && !disableErrorMessage && <b className='text-red-500 font-light text-sm'>- {errorMessage}</b>}</p>
      <input
        {...register(name)}
        placeholder={placeholder}
        type={type}
        disabled={disabled}
        min={minDate}
        max={maxDate}
        minLength={minLength}
        maxLength={maxLength}
        onChange={({target}) => {
          if(setValue) {
            setValue(target.value)
          }
        }}
        onBlur={onBlur}
        className={`h-12 w-full px-4 ${disabled ? 'text-zinc-400' : 'text-white'} rounded-xl ${error ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
      />
    </div>
  )
}
