import { useEffect } from 'react'
import { UseFormRegister } from 'react-hook-form'

import './style.css'

interface IProps {
  label: string
  setValue?: (d: any) => void
  error?: boolean
  errorMessage?: string
  width?: string
  register: UseFormRegister<any>
  name: string
  placeholder?: string
  type?: 'text' | 'date' | 'number'
  disabled?: boolean
  minDate?: string
  minLength?: number
  maxLength?: number
  maxDate?: string
  disableErrorMessage?: boolean
  onBlur?: () => void
  value?: string | number
}

export default function InputText({
  label,
  setValue,
  error,
  errorMessage,
  width = 'auto',
  register,
  name,
  placeholder = '',
  type = 'text',
  disabled = false,
  minDate,
  minLength,
  maxLength,
  maxDate,
  disableErrorMessage = false,
  onBlur,
  value
}: IProps) {
  return (
    <div className="input relative group" style={{ width }}>
      <p className="text-white mb-1 text-sm">
        {label}
        {error && !disableErrorMessage && (
          <b className="text-red-500 font-light text-sm"> - {errorMessage}</b>
        )}
      </p>
      <input
        {...register(name)}
        placeholder={placeholder}
        type={type}
        id={name}
        disabled={disabled}
        min={minDate}
        max={maxDate}
        minLength={minLength}
        maxLength={maxLength}
        {...(setValue
          ? { onChange: ({ target }) => setValue(target.value) }
          : {})}
        onBlur={onBlur}
        className={`h-12 w-full px-4 ${
          disabled ? 'text-zinc-400' : 'text-white'
        } rounded-xl ${
          error ? 'ring-[#f33636]' : 'ring-[#FF9900]'
        } ring-1 ring-inset bg-black flex-1`}
        {...(value !== undefined ? { value } : {})}
      />
      {error && (
        <div className="absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block">
          {errorMessage}
        </div>
      )}
    </div>
  )
}
