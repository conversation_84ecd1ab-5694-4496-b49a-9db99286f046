"use client"

import Header from '@/components/Header'
import Sidebar from '@/components/Sidebar'
import PageHeader from '@/components/PageHeader'
import List from '@/components/List'
import { useEffect, useState } from 'react'
import api from '@/core/api'
import { toast } from 'react-toastify'
import { AdjustmentsHorizontalIcon, ArrowRightCircleIcon, ArrowRightIcon, ArrowTopRightOnSquareIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, ChevronUpIcon, DocumentTextIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/24/outline'
import InputSearch from '@/components/InputSearch'
import User, { UserProfile } from '@/models/user'
import { useRouter } from 'next/navigation'
import Contact from '@/models/contract'
import { clearLetters, cnpjMask, cpfMask } from '@/utils/masks'
import formatDate from '@/functions/formatDate'
import RenewContract from './compose/RenewContract'
import ModalContract from './compose/ModalContract'
import AddPayment from './compose/AddPayment'
import Button from '@/components/Button/Button'
import AditiveContract from './compose/AditiveModal'
import { SkeletonItem } from '@/components/Skeleton/intex'
import Pagination from '@/components/Pagination'
import { ContractStatus, formatStatusContract } from '@/functions/formatStatus'
import StatusWithDescription from '@/components/StatusWithDescription'
import { getUserProfile } from '@/functions/getUserData'
import TableFormat from '@/components/Table/components/TableFormat'
import FilterModal from '@/components/FilterModal'
import Select from '@/components/Select'
import Table from '@/components/Table'
import filterStatusContracts from '@/constants/filterStatusContract'
import SelectCustom from "@/components/SelectCustom";

interface Options {
  total: string,
  perPage: string,
  page: string,
  lastPage: string,
}

export default function MeusContratos() {
  const router = useRouter()
  const [data, setData] = useState<Contact[]>([])
  const [contract, setContract] = useState<Contact>()
  const [loading, setLoading] = useState<boolean>(false)
  const [modalFilter, setModalFilter] = useState<boolean>(false)
  const [modal, setModal] = useState<boolean>(false)
  const [modalPayment, setModalPayment] = useState<boolean>(false)
  const [renew, setRenew] = useState<boolean>(false)
  const [optSelected, setOptSelected] = useState('Todos')
  const [page, setPage] = useState<number>(1)
  const [pageOptions, setPageOptions] = useState<Options>()
  const [signatarie, setSignatarie] = useState<string>('')
  const [filterData, setFilterData] = useState({
    startData: '',
    endData: '',
    type: 'all'
  })

  const [modalCreateContract, setModalCreateContract] = useState(false)

  const userProfile = getUserProfile()

  useEffect(() => {
    getContracts(signatarie)
  }, [page])

  useEffect(() => {
    localStorage.setItem('typeCreateContract', '')
  }, [])

  const returnRoute = () => {
    switch (userProfile.name) {
      case 'admin':
        return '/admin/list-contracts'
      case 'superadmin':
        return '/contract/list-contracts/superadmin'
      default:
        return ''
    }
  }

  const getContracts = (document?: string) => {
    setLoading(true)
    api.get(returnRoute(), {
      params: {
        roleId: userProfile.roleId,
        limit: '10',
        page,
        status: optSelected === 'Todos' ? undefined : optSelected,
        signatarie: document ? clearLetters(document || '') : undefined,
        dateFrom: filterData.startData === '' ? undefined : filterData.startData,
        dateTo: filterData.endData === '' ? undefined : filterData.endData,
        contractType: filterData.type === 'all' ? undefined : filterData.type
      }
    }).then(resp => {
      if(userProfile.name === 'admin' || userProfile.name === 'superadmin') {
        setData(resp.data.documentos)
        setPageOptions({
          total: resp?.data?.total,
          perPage: resp?.data?.totalPorPagina,
          page: resp?.data?.paginaAtual,
          lastPage: resp?.data?.totalPaginas,
        })
      } else {
        setData(resp.data.data)
        setPageOptions({
          total: resp?.data?.total,
          perPage: resp?.data?.limit,
          page: resp?.data?.page,
          lastPage: resp?.data?.totalPages,
        })
      }
      
    }).catch(error => {
    }).finally(() => setLoading(false))
  }

  const translateTag = (tag: string) => {
    if(tag?.toUpperCase() === 'P2P') {
      return 'MUTUO'
    } else {
      return tag.toUpperCase()
    }
  }

  const filterTypeContractOptions = [
    {
      label: "Todos",
      value: "all"
    },
    {
      label: "Contratos SCP",
      value: "SCP"
    },
    {
      label: "Contratos Mútuo",
      value: "MUTUO"
    },
  ]
  
  return (
    <div className={modal || modalCreateContract ? 'fixed w-full' : 'relative'}>
      <Header />
      <Sidebar>
      <>
      <div className='w-full text-white flex flex-col flex-wrap gap-2'>
        <h1 className='m-auto font-bold text-2xl'>Contratos</h1>
      </div>
        
      <TableFormat>
          <div className='w-full p-2 gap-4'>
            <div className="flex flex-1 w-full justify-between items-center gap-4 mb-2">
              {
                (userProfile.name === 'advisor' || userProfile.name === 'broker') && (
                  <div className='w-32 h-10 bg-orange-linear px-10 flex items-center justify-center md:mr-5 mb-2 md:mb-0 rounded-lg cursor-pointer' onClick={() => {
                    if (userProfile.name === 'advisor') {
                      localStorage.setItem('typeCreateContract', 'broker')
                      router.push('/meus-contratos/registro-manual')
                    } else {
                      setModalCreateContract(true)
                    }
                  }}>
                    <p>Criar</p>
                  </div>
                )
              }
              <div className='flex items-center gap-4 w-full justify-end'>
                <div className="md:w-3/12">
                  <InputSearch
                    handleSearch={() => {getContracts(signatarie)}}
                    setValue={setSignatarie}
                    placeholder='Pesquisar por CPF/CNPJ'
                    value={signatarie}
                  />
                </div>
                <FilterModal
                  activeModal={modalFilter}
                  setActiveModal={setModalFilter}
                  handleSearch={() => {
                    getContracts()
                  }}
                >
                  <div className="flex flex-col justify-between mt-2">
                    <div className='flex md:flex-row flex-col justify-between mb-4'>
                      <div className='mb-2 md:mb-0'>
                        <p className='text-xs'>Início</p>
                        <input 
                          value={filterData.startData} 
                          className='p-1 rounded-md text-xs bg-transparent border' 
                          onChange={({target}) => setFilterData({
                            ...filterData,
                            startData: target.value
                          })} 
                          type="date" 
                        />
                      </div>
                      <div className=''>
                        <p className='text-xs'>Fim</p>
                        <input
                          value={filterData.endData} 
                          className='p-1 rounded-md text-xs bg-transparent border' 
                          onChange={({target}) => setFilterData({
                            ...filterData,
                            endData: target.value
                          })} 
                          type="date" 
                        />
                      </div>
                    </div>
                    <div className="w-full">
                      <p className='text-xs'>Tipo de contrato</p>
                      <SelectCustom
                        value={filterData.type}
                        onChange={(e) => setFilterData({...filterData, type: e.target.value})}
                      >
                        {filterTypeContractOptions.map((option, index) => (
                          <option key={index} value={option.value}>{option.label}</option>
                        ))}
                      </SelectCustom>
                    </div>
                    <div className="w-full mt-4">
                      <p className='text-xs'>Status do contrato</p>
                      <SelectCustom
                        value={optSelected}
                        onChange={(e) => setOptSelected(e.target.value)}
                      >
                        <option value="Todos">Todos</option>
                        {filterStatusContracts.map((status, index) => (
                          <option key={index} value={status.value}>{status.label}</option>
                        ))}
                      </SelectCustom>
                    </div>
                  </div>
                </FilterModal>
              </div>
            </div>
            <Table 
              data={data}
              headers={[
                {
                  title: "",
                  component: "id",
                  width: "30px",
                  render: (_, row) => (
                    <div className='cursor-pointer' onClick={() => {
                      setModal(true)
                      setContract(row)
                    }}>
                      <ArrowTopRightOnSquareIcon color={'#fff'} width={20} />
                    </div>
                  )
                },
                {
                  title: "Investidor",
                  component: "nomeInvestidor",
                  width: "150px"
                },
                {
                  title: "CPF/CNPJ",
                  component: "document",
                  position: "center",
                  width: "150px",
                  render: (_, row) => (
                    <p className='text-center'>{clearLetters(row.documentoInvestidor || '').length <= 11 ? cpfMask(row.documentoInvestidor || '') : cnpjMask(row.documentoInvestidor || '')}</p>
                  )
                },
                {
                  title: "Valor",
                  component: "valorInvestimento",
                  position: "center",
                  render: (item) => (
                    <p className='text-center'>{
                      Number(item || 0).toLocaleString('pt-br', {
                        style: 'currency',
                        currency: 'BRL'
                      })
                    }</p>
                  )
                },
                {
                  title: "Rendimento",
                  component: "rendimentoInvestimento",
                  position: "center",
                  render: (item) => (
                    <p className='text-center'>{String(item) || '0'}%</p>
                  )
                },
                {
                  title: "Consultor",
                  component: "consultorResponsavel",
                  position: "center",
                  width: "100px"
                },
                {
                  title: "Criado em",
                  component: "inicioContrato",
                  position: "center",
                  render: (item) => (
                    <p className='text-center'>{formatDate(String(item))}</p>
                  )
                },
                {
                  title: "Status",
                  component: "statusContrato",
                  position: "center",
                  width: "100px",
                  render: (_, row) => (
                    <StatusWithDescription 
                      description={formatStatusContract(row.statusContrato).description}
                      text={formatStatusContract(row.statusContrato).title}
                      textColor={formatStatusContract(row.statusContrato).textColor}
                    />
                  )
                },
                {
                  title: "Modelo",
                  component: "inicioContrato",
                  position: "center",
                  render: (item, row) => (
                    <div className='px-2'>
                      <div className='bg-white py-[5px] px-[10px] rounded-md text-center'>
                        <p className='text-xs text-[#FF9900] font-bold'>{translateTag(row.tags) || 'NE'}</p>
                      </div>
                    </div>
                  )
                },
              ]}
              loading={loading}
              pagination={{
                page,
                lastPage: Number(pageOptions?.lastPage),
                perPage: 10,
                setPage: setPage,
                totalItems: String(pageOptions?.total)
              }}
            />
          </div>
        </TableFormat>

        {
          modal && contract && (
            <ModalContract contract={contract} setRenew={setRenew} setModal={setModal} setModalPayment={setModalPayment} />
          )
        }
        {
          modalPayment && contract && (
            <AddPayment contract={contract} setOpenModal={setModalPayment} documentSearch={signatarie} getContracts={getContracts} />
          )
        }
        {
          renew && contract && (
            <RenewContract contract={contract} setOpenModal={setRenew} />
          )
        }
        {
          modalCreateContract && (
            <div className='fixed z-40 top-0 left-0 w-full h-full bg-[#3A3A3AAB]'>
              <div className='absolute w-3/6 bg-[#1C1C1C] z-50 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] border border-[#FF9900] rounded-lg'>
                <div className='p-4 text-white flex justify-between items-center border-b border-[#FF9900]'>
                  <div>
                    <h3 className='font-bold text-base mb-1'>Novo Contrato Individual</h3>
                    <p className='text-xs w-7/12'>Clique aqui para criar um novo contrato com negociação exclusiva para você.</p>
                  </div>
                  <div className='bg-orange-linear p-2 rounded-full cursor-pointer animate-moveXRight' onClick={() => {
                    localStorage.setItem('typeCreateContract', 'broker')
                    router.push('/meus-contratos/registro-manual')
                  }}>
                    <ArrowRightIcon width={20} /> 
                  </div>
                </div>
                <div className='p-4 text-white flex justify-between items-center'>
                  <div>
                    <h3 className='font-bold text-base mb-1'>Novo Contrato Compartilhado</h3>
                    <p className='text-xs w-7/12'>Clique aqui para criar um novo contrato com a negociação personalizada de múltiplos assessores.</p>
                  </div>
                  <div className='bg-orange-linear p-2 rounded-full cursor-pointer' onClick={() => {
                    localStorage.setItem('typeCreateContract', 'advisors')
                    router.push('/meus-contratos/registro-manual')
                  }}>
                    <ArrowRightIcon width={20} />
                  </div>
                </div>
              </div>
            </div>
          )
        }
      </>
      </Sidebar>
    </div>
  )
}
