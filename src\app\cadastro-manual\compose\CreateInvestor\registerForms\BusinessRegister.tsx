"use client";

import InputText from "@/components/Inputs/InputText";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm, SubmitHandler } from "react-hook-form";
import { schemaRegister } from "@/utils/schemas/schemasValidation";
import Button from "@/components/Button/Button";
import {
  cepMask,
  clearLetters,
  cnpjMask,
  cpfMask,
  phoneMask,
  valueMask,
} from "@/utils/masks";
import { createPJContract } from "@/utils/schemas/createContract";
import { useEffect, useState } from "react";
import InputSelect from "@/components/Inputs/InputSelect";
import InputTextArea from "@/components/Inputs/InputTextArea";
import axios from "axios";
import { UserProfile } from "@/models/user";
import { signIca } from "@/constants/signIca";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import { toast } from "react-toastify";
import SelectSearch from "@/components/SelectSearch";
import Input from "@/components/Input";
import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import CoverForm from "@/components/CoverForm";
import { getFinalDataWithMount } from "@/functions/getDataFilter";
import moment from "moment";
import momentTz from "moment-timezone";
import formatNumberValue from "@/utils/formatNumberValue";
import { isValidateCNPJ, isValidateCPF } from "@/utils/validate-documents";
import { getUserProfile } from "@/functions/getUserData";
import validatePhoneNumber from "@/utils/validatePhoneNumber";
import { isUnderage } from "@/utils/isUnderage";
import isValidUF from "@/utils/isValidUf";
import { InferType } from "yup";
import Dropzone from "@/components/Dropzone";

interface IProps {
  modalityContract: "MUTUO" | "SCP";
}

interface AdvisorsTableItem {
  id: string;
  taxValue: number | string;
  generatedId: string;
}

interface BrokerAcessor {
  name: string;
  id: string;
  rate: string;
}

const companyTypes = [
  {
    label: "MEI",
    value: "MEI",
  },
  {
    label: "EI",
    value: "EI",
  },
  {
    label: "EIRELI",
    value: "EIRELI",
  },
  {
    label: "SLU",
    value: "SLU",
  },
  {
    label: "LTDA",
    value: "LTDA",
  },
  {
    label: "SA",
    value: "SA",
  },
  {
    label: "SS",
    value: "SS",
  },
  {
    label: "CONSORCIO",
    value: "CONSORCIO",
  },
];

export default function BusinessRegister({ modalityContract }: IProps) {
  const [confirm, setConfirm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [zipCode, setZipCode] = useState("");
  const [companyZipCode, setCompanyZipCode] = useState("");
  const [startDate, setStartDate] = useState("");
  const [investDate, setInvestDate] = useState("");

  const [contract, setContract] = useState<FileList>();
  const [file, setFile] = useState<FileList>();
  const [document, setDocumet] = useState<FileList>();
  const [residence, setResidence] = useState<FileList>();
  const [brokers, setBrokers] = useState<BrokerAcessor[]>([]);

  const [brokerIdSelected, setBrokerIdSelected] = useState("");

  const [acessors, setAcessors] = useState<BrokerAcessor[]>([]);
  const [loadingAcessors, setLoadingAcessors] = useState(false);
  const [advisors, setAdvisors] = useState<AdvisorsTableItem[]>([]);

  const userProfile = getUserProfile();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver(createPJContract),
    mode: "all",
    defaultValues: {
      isSCP: modalityContract === "SCP",
      purchaseWith: "pix",
      profile: "moderate",
      isDebenture: "s",
    },
  });

  const initDate = watch("initDate");
  const term = watch("term");

  useEffect(() => {
    if (initDate && term) {
      const endData = getFinalDataWithMount({
        investDate: term,
        startDate: initDate,
      });
      setValue("endDate", endData, {shouldValidate: true});
    }
  }, [initDate, term, setValue]);

  function getBrokers() {
    api
      .get(
        userProfile.name === "superadmin"
          ? "/wallets/list-brokers"
          : "/wallets/admin/brokers"
      )
      .then((resp) => {
        setBrokers(resp.data);
      })
      .catch((error) => {
        returnError(error, "Erro ao buscar a lista de brokers");
      });
  }

  function getRouteAdivsorSearch() {
    switch (userProfile.name) {
      case "superadmin":
        return "/wallets/list-advisors-broker";
      case "admin":
        return "/wallets/admin/advisors-broker";
      case "broker":
        return "/wallets/broker/advisors";
      default:
        return "";
    }
  }

  function getAcessors() {
    setLoadingAcessors(true);
    api
      .get(getRouteAdivsorSearch(), {
        params: {
          brokerId:
            userProfile.name !== "broker" ? brokerIdSelected : undefined,
        },
      })
      .then((resp) => {
        setAcessors(resp.data);
      })
      .catch((error) => {
        returnError(error, "Erro ao buscar a lista de brokers");
      })
      .finally(() => setLoadingAcessors(false));
  }
  useEffect(() => {
    if (userProfile.name !== "broker" && userProfile.name !== "advisor") {
      getBrokers();
    }
  }, []);

  useEffect(() => {
    if (
      (userProfile.name !== "broker" && brokerIdSelected !== "") ||
      userProfile.name === "broker"
    ) {
      getAcessors();
    }
  }, [brokerIdSelected]);

  useEffect(() => {
    setValue("isSCP", modalityContract === "SCP", {shouldValidate: true});
  }, [modalityContract]);

  const onSubmit = (data: any) => {
    const yeld = Number(data.yield.replace(",", "."));

    setLoading(true);

    toast.info("Cadastrando investidor...");

    const form = new FormData();

    // Payload Principal
    form.append("personType", "PJ");
    form.append("contractType", modalityContract);
    form.append("role", userProfile.name);

    if (userProfile.name !== "broker") {
      form.append("brokerId", brokerIdSelected);
    }

    // Dados Bancários
    form.append("bankAccount[bank]", data.bank);
    form.append("bankAccount[agency]", data.agency);
    form.append("bankAccount[account]", data.accountNumber);
    form.append("bankAccount[pix]", data.pix);
    form.append("bankAccount[accountType]", "CORRENTE");

    // Dados de investimento
    form.append("investment[amount]", String(formatNumberValue(data.value)));
    form.append("investment[monthlyRate]", String(yeld));
    form.append("investment[durationInMonths]", data.term);
    form.append("investment[paymentMethod]", data.purchaseWith);
    form.append("investment[startDate]", `${data.initDate}T00:00:00.000Z`);
    form.append(
      "investment[endDate]",
      `${moment(data.endDate, "DD/MM/YYYY").format("YYYY-MM-DD")}T00:00:00.000Z`
    );
    form.append("investment[profile]", data.profile);
    form.append(
      "investment[isDebenture]",
      data.isDebenture === "s" ? "true" : "false"
    );
    if (modalityContract === "SCP") {
      form.append("investment[quotaQuantity]", data.amountQuotes);
    }

    // Dados da compania
    form.append("company[corporateName]", data.name);
    form.append("company[cnpj]", clearLetters(data.document));
    form.append("company[type]", data.companyType);

    // endereço da empresa
    form.append("company[address][street]", data.companyStreet);
    form.append("company[address][city]", data.companyCity);
    form.append("company[address][state]", data.companyState);
    form.append("company[address][neighborhood]", data.companyNeighborhood);
    form.append(
      "company[address][postalCode]",
      clearLetters(data.companyZipCode)
    );
    form.append("company[address][number]", data.companyNumber);
    form.append("company[address][complement]", data.companyComplement);

    // Representante da empresa
    form.append("company[representative][fullName]", data.ownerName);
    form.append(
      "company[representative][cpf]",
      clearLetters(data.ownerDocument)
    );
    form.append("company[representative][rg]", data.rg);
    form.append("company[representative][issuingAgency]", data.issuingAgency);
    form.append("company[representative][nationality]", data.placeOfBirth);
    form.append("company[representative][occupation]", data.occupation);
    form.append("company[representative][birthDate]", data.dtBirth);
    form.append("company[representative][email]", data.email);
    form.append(
      "company[representative][phone]",
      clearLetters(data.phoneNumber)
    );
    form.append("company[representative][motherName]", data.motherName);

    // Endereço do Representante
    form.append("company[representative][address][street]", data.street);
    form.append("company[representative][address][city]", data.city);
    form.append("company[representative][address][state]", data.state);
    form.append(
      "company[representative][address][neighborhood]",
      data.neighborhood
    );
    form.append(
      "company[representative][address][postalCode]",
      clearLetters(data.zipCode)
    );
    form.append("company[representative][address][number]", data.number);
    form.append(
      "company[representative][address][complement]",
      data.complement
    );

    // Adicionando os assessores.
    if (
      advisors.length === 1 ||
      (advisors.length > 1 && advisors[0].id !== "")
    ) {
      advisors.forEach((advisor, index) => {
        form.append(`advisors[${index}][advisorId]`, advisor.id);
        form.append(`advisors[${index}][rate]`, String(advisor.taxValue));
      });
    }

    // Documentos
    if (contract) {
      form.append("contract", contract[0]);
    }
    if (file) {
      form.append("proofOfPayment", file[0]);
    }
    if (document) {
      form.append("personalDocument", document[0]);
    }
    if (residence) {
      form.append("proofOfResidence", residence[0]);
    }

    api
      .post("/account/create/existing-contract", form)
      .then((resp) => {
        toast.success("Investidor cadastrado com sucesso!");
        reset();
        setAdvisors([]);
        setDocumet(undefined);
        setResidence(undefined);
        setContract(undefined);
        setFile(undefined);
      })
      .catch((err) => {
        returnError(err, "Erro ao cadastrar o contrato!");
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    getCep(zipCode || "", "person");
  }, [zipCode]);

  useEffect(() => {
    getCep(companyZipCode || "", "company");
  }, [companyZipCode]);

  async function handleGetByCep(ev: string, type: "company" | "person") {
    const value = ev;

    const cep = value.replace(/[^0-9]/g, "");

    if (cep.length !== 8) {
    } else {
      await axios
        .get(`https://viacep.com.br/ws/${cep}/json/`)
        .then((response) => {
          if (response && response.data) {
            if (response.data.erro) {
            } else {
              if (type === "person") {
                setValue("neighborhood", response.data.bairro, {shouldValidate: true});
                setValue("street", response.data.logradouro, {shouldValidate: true});
                setValue("city", response.data.localidade, {shouldValidate: true});
                setValue("state", response.data.uf, {shouldValidate: true});
              } else {
                setValue("companyNeighborhood", response.data.bairro, {shouldValidate: true});
                setValue("companyStreet", response.data.logradouro, {shouldValidate: true});
                setValue("companyCity", response.data.localidade, {shouldValidate: true});
                setValue("companyState", response.data.uf, {shouldValidate: true});
              }
            }
          }
        })
        .catch(() => {})
        .finally(() => {});
    }
  }

  function getCep(value: string, type: "company" | "person") {
    const number = value;
    const cep = number.replace(/[^0-9]/g, "");
    if (cep.length === 8) {
      handleGetByCep(cep, type);
    } else {
    }
  }

  const handleInputChange = (id: string, value: string) => {
    setAdvisors((prevItems) =>
      prevItems.map((item) =>
        item.generatedId === id ? { ...item, taxValue: value } : item
      )
    );
  };

  return (
    <div>
      <form
        action=""
        onSubmit={handleSubmit(onSubmit)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault(); // Bloqueia o envio ao pressionar Enter
          }
        }}
      >
        <CoverForm color="black" title={"Dados Pessoais - Representante"}>
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="ownerName"
              width="300px"
              error={!!errors.ownerName}
              errorMessage={errors?.ownerName?.message}
              label="Nome"
            />
            <InputText
              register={register}
              name="ownerDocument"
              width="200px"
              error={!!errors.ownerDocument}
              errorMessage={errors?.ownerDocument?.message}
              label="CPF"
              setValue={(e) => setValue("ownerDocument", cpfMask(e || ""), {shouldValidate: true})}
            />
            <InputText
              register={register}
              name="rg"
              width="200px"
              error={!!errors.rg}
              errorMessage={errors?.rg?.message}
              label="RG"
            />
            <InputText
              register={register}
              name="issuer"
              width="200px"
              error={!!errors.issuer}
              errorMessage={errors?.issuer?.message}
              label="Orgão emissor"
            />
            <InputText
              register={register}
              name="placeOfBirth"
              width="200px"
              error={!!errors.placeOfBirth}
              errorMessage={errors?.placeOfBirth?.message}
              label="Nacionalidade"
            />
            <InputText
              register={register}
              name="occupation"
              width="200px"
              error={!!errors.occupation}
              errorMessage={errors?.occupation?.message}
              label="Ocupação"
            />
            <InputText
              register={register}
              name="motherName"
              width="250px"
              error={!!errors.motherName}
              errorMessage={errors?.motherName?.message}
              label="Nome da mãe"
            />
            <InputText
              type="date"
              register={register}
              name="dtBirth"
              width="200px"
              error={!!errors.dtBirth}
              errorMessage={errors?.dtBirth?.message}
              label="Data de Nascimento"
            />
            <InputText
              width="200px"
              register={register}
              name="phoneNumber"
              error={!!errors.phoneNumber}
              errorMessage={errors?.phoneNumber?.message}
              label="Celular"
              maxLength={15}
              setValue={(e) => setValue("phoneNumber", phoneMask(e || ""), {shouldValidate: true})}
            />
            <InputText
              register={register}
              name="email"
              width="300px"
              error={!!errors.email}
              errorMessage={errors?.email?.message}
              label="E-mail"
            />
            <InputText
              register={register}
              name="zipCode"
              width="200px"
              error={!!errors.zipCode}
              errorMessage={errors?.zipCode?.message}
              label="CEP"
              setValue={(e) => {
                setZipCode(e);
                setValue("zipCode", cepMask(e), {shouldValidate: true});
              }}
            />
            <InputText
              register={register}
              name="neighborhood"
              width="300px"
              error={!!errors.neighborhood}
              errorMessage={errors?.neighborhood?.message}
              label="Bairro"
            />
            <InputText
              register={register}
              name="street"
              width="300px"
              error={!!errors.street}
              errorMessage={errors?.street?.message}
              label="Rua"
            />

            <InputText
              register={register}
              name="city"
              width="200px"
              error={!!errors.city}
              errorMessage={errors?.city?.message}
              label="Cidade"
            />
            <InputText
              register={register}
              maxLength={2}
              setValue={(e) => setValue("state", String(e).toUpperCase(), {shouldValidate: true})}
              name="state"
              width="150px"
              error={!!errors.state}
              errorMessage={errors?.state?.message}
              label="Estado"
            />
            <InputText
              register={register}
              name="number"
              width="200px"
              error={!!errors.number}
              errorMessage={errors?.number?.message}
              label="Número"
            />
            <InputText
              register={register}
              name="complement"
              width="200px"
              error={!!errors.complement}
              errorMessage={errors?.complement?.message}
              label="Complemento"
            />
          </div>
        </CoverForm>
        <CoverForm color="black" title="Dados da empresa">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="name"
              width="400px"
              error={!!errors.name}
              errorMessage={errors?.name?.message}
              label="Razão Social"
            />
            <InputText
              register={register}
              name="document"
              width="200px"
              error={!!errors.document}
              errorMessage={errors?.document?.message}
              label="CNPJ"
              setValue={(e) => setValue("document", cnpjMask(e || ""), {shouldValidate: true})}
            />
            {/* <InputText type="date" register={register} name="dtBirth" width="200px" error={!!errors.dtOpening} errorMessage={errors?.dtOpening?.message} label="Data de Abertura" /> */}
            <InputSelect
              width="200px"
              name="companyType"
              register={register}
              options={companyTypes}
              error={!!errors.companyType}
              errorMessage={errors?.companyType?.message}
              label="Tipo"
            />
            <InputText
              register={register}
              name="companyZipCode"
              width="200px"
              error={!!errors.companyZipCode}
              errorMessage={errors?.companyZipCode?.message}
              label="CEP"
              setValue={(e) => {
                setCompanyZipCode(e);
                setValue("companyZipCode", cepMask(e), {shouldValidate: true});
              }}
            />
            <InputText
              register={register}
              name="companyNeighborhood"
              width="300px"
              error={!!errors.companyNeighborhood}
              errorMessage={errors?.companyNeighborhood?.message}
              label="Bairro"
            />
            <InputText
              register={register}
              name="companyStreet"
              width="300px"
              error={!!errors.companyStreet}
              errorMessage={errors?.companyStreet?.message}
              label="Rua"
            />
            <InputText
              register={register}
              name="companyCity"
              width="200px"
              error={!!errors.companyCity}
              errorMessage={errors?.companyCity?.message}
              label="Cidade"
            />
            <InputText
              register={register}
              maxLength={2}
              setValue={(e) =>
                setValue("companyState", String(e).toUpperCase(), {shouldValidate: true})
              }
              name="companyState"
              width="150px"
              error={!!errors.companyState}
              errorMessage={errors?.companyState?.message}
              label="Estado"
            />
            <InputText
              register={register}
              name="companyNumber"
              width="200px"
              error={!!errors.companyNumber}
              errorMessage={errors?.companyNumber?.message}
              label="Número"
            />
            <InputText
              register={register}
              name="companyComplement"
              width="200px"
              error={!!errors.companyComplement}
              errorMessage={errors?.companyComplement?.message}
              label="Complemento"
            />
          </div>
        </CoverForm>
        <CoverForm color="black" title="Dados de Investimento">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="value"
              width="200px"
              error={!!errors.value}
              errorMessage={errors?.value?.message}
              label="Valor"
              setValue={(e) => setValue("value", valueMask(e || ""), {shouldValidate: true})}
            />
            <InputText
              register={register}
              type="number"
              name="term"
              width="250px"
              setValue={(e) => setValue("term", e, {shouldValidate: true})}
              error={!!errors.term}
              errorMessage={errors?.term?.message}
              label="Prazo investimento - em meses"
              placeholder="ex: 12"
            />
            <InputText
              register={register}
              type="text"
              name="yield"
              width="250px"
              error={!!errors.yield}
              errorMessage={errors?.yield?.message}
              label="Taxa Remuneração Mensal - em %"
              placeholder="ex: 2"
            />
            <InputSelect
              width="200px"
              name="purchaseWith"
              register={register}
              options={[
                { label: "PIX", value: "pix" },
                { label: "Boleto", value: "boleto" },
                { label: "Transferência", value: "bank_transfer" },
              ]}
              error={!!errors?.purchaseWith}
              errorMessage={errors?.purchaseWith?.message}
              label="Comprar com"
            />
            {modalityContract === "SCP" && (
              <>
                <InputText
                  register={register}
                  type="number"
                  name="amountQuotes"
                  width="150px"
                  error={!!errors.amountQuotes}
                  errorMessage={errors?.amountQuotes?.message}
                  label="Quantidade de cotas"
                />
              </>
            )}
            <InputText
              type="date"
              register={register}
              maxDate={moment().format("YYYY-MM-DD")}
              name="initDate"
              width="200px"
              setValue={(e) => setValue("initDate", e, {shouldValidate: true})}
              error={!!errors.initDate}
              errorMessage={errors?.initDate?.message}
              label="Inicio do contrato"
            />
            <InputText
              type="text"
              register={register}
              name="endDate"
              width="200px"
              value={watch("endDate")}
              error={!!errors.endDate}
              errorMessage={errors?.endDate?.message}
              disabled={true}
              label="Final do contrato"
            />
            <InputSelect
              width="200px"
              name="profile"
              register={register}
              options={[
                { label: "Conservador", value: "conservative" },
                { label: "Moderado", value: "moderate" },
                { label: "Agressivo", value: "aggressive" },
              ]}
              error={!!errors.profile}
              errorMessage={errors?.profile?.message}
              label="Perfil"
            />
            <InputSelect
              width="100px"
              name="isDebenture"
              register={register}
              options={[
                { label: "Sim", value: "s" },
                { label: "Não", value: "n" },
              ]}
              error={!!errors.isDebenture}
              errorMessage={errors?.isDebenture?.message}
              label="Debênture"
            />
          </div>
        </CoverForm>
        {userProfile.name !== "broker" && (
          <CoverForm color="black" title="Selecione o broker">
            <div className="flex md:flex-row flex-col w-full gap-4 justify-start">
              <div className="md:w-2/4">
                <SelectSearch
                  label="Broker"
                  items={brokers}
                  value={brokerIdSelected}
                  setValue={setBrokerIdSelected}
                />
              </div>
            </div>
          </CoverForm>
        )}
        {((userProfile.name !== "broker" && brokerIdSelected !== "") ||
          userProfile.name === "broker") && (
          <CoverForm color="black" title="Adicionar Assessor">
            <div>
              {advisors.length > 0 ? (
                advisors?.map((advisor, i) => (
                  <div
                    key={i}
                    className="flex justify-between items-end gap-4 mb-4"
                  >
                    <div className="flex-1">
                      <SelectSearch
                        label="Selecione um Assessor"
                        items={acessors}
                        value={""}
                        setValue={() => {}}
                        loading={loadingAcessors}
                        handleChange={(test) => {
                          const advisorFilter = advisors.filter(
                            (adv) => adv.generatedId === advisor.generatedId
                          )[0];
                          const newAdvisor = {
                            generatedId: advisorFilter.generatedId,
                            id: test.id,
                            taxValue: Number(test.rate),
                          };
                          const newArray = advisors;
                          newArray[i] = newAdvisor;
                          setAdvisors([...newArray]);
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <Input
                        label="Adicione a Taxa - em %"
                        id=""
                        name=""
                        value={String(advisor.taxValue)}
                        type="text"
                        onChange={(e) =>
                          handleInputChange(advisor.generatedId, e.target.value)
                        }
                      />
                    </div>
                    <div
                      className="bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full"
                      onClick={() => {
                        const newArray = advisors.filter(
                          (_, index) => index !== i
                        );
                        setAdvisors(newArray);
                      }}
                    >
                      <TrashIcon width={20} />
                    </div>
                  </div>
                ))
              ) : (
                <div>
                  <p className="text-white">Nenhum assessor adicionado!</p>
                </div>
              )}
              <div
                className="bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5"
                onClick={() => {
                  setAdvisors([
                    ...advisors,
                    {
                      generatedId: crypto.randomUUID(),
                      id: "",
                      taxValue: "",
                    },
                  ]);
                }}
              >
                <PlusIcon width={25} color="#fff" />
              </div>
            </div>
          </CoverForm>
        )}
        <CoverForm color="black" title="Dados bancarios">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="bank"
              width="300px"
              error={!!errors.bank}
              errorMessage={errors?.bank?.message}
              label="Banco"
            />
            <InputText
              register={register}
              name="agency"
              width="200px"
              error={!!errors.agency}
              errorMessage={errors?.agency?.message}
              label="Agência"
            />
            <InputText
              register={register}
              name="accountNumber"
              width="200px"
              error={!!errors.accountNumber}
              errorMessage={errors?.accountNumber?.message}
              label="Conta"
            />
            <InputText
              register={register}
              name="pix"
              width="250px"
              error={!!errors.pix}
              errorMessage={errors?.pix?.message}
              label="Pix"
            />
          </div>
        </CoverForm>

        <CoverForm color="black" title="Anexo de documentos">
          <div>
            <div className="flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white">
              <div>
                <p className="mb-1">Contrato</p>
                <Dropzone onFileUploaded={setContract} />
              </div>
              <div>
                <p className="mb-1">Comprovante</p>
                <div>
                  <Dropzone onFileUploaded={setFile} />
                </div>
              </div>
              <div>
                <p className="mb-1">Documento de identidade</p>
                <div>
                  <Dropzone onFileUploaded={setDocumet} />
                </div>
              </div>
              <div>
                <p className="mb-1">Comprovante de residência</p>
                <div>
                  <Dropzone onFileUploaded={setResidence} />
                </div>
              </div>
            </div>
          </div>
        </CoverForm>

        <div className="md:w-52 mb-10">
          <Button
            label="Enviar"
            size="lg"
            loading={loading}
            disabled={
              loading ||
              !isValid ||
              !contract ||
              !file ||
              !document ||
              !residence
            }
          />
        </div>
      </form>
    </div>
  );
}
