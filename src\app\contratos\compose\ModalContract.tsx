import api from "@/core/api";
import Contact, { Addendum } from "@/models/contract";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import ContractData from "./ContractData";
import AditiveContract from "./AditiveModal";
import AditiveData from "./AditiveData";
import { getUserProfile } from "@/functions/getUserData";
import { ContractStatus } from "@/functions/formatStatus";
import { useNavigation } from "@/hooks/navigation";
import { Button } from "@/components/ui/button";

interface IProps {
  contract: Contact | any
  setRenew: (d:any) => void
  setModal: (d:any) => void
  setModalPayment: (d:any) => void
}

export default function ModalContract({ contract, setModal, setRenew, setModalPayment }: IProps) {
  const [step, setStep] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [aditives, setAditives] = useState<Addendum[]>([])
  const [aditive, setAditive] = useState<boolean>(false)
  const {navigation} = useNavigation()

  const userProfile = getUserProfile()

  const resendContract = () => {
    setLoading(true)
    api.post(`/contract/send-notification/${contract.idContrato}`).then(resp => {
      toast.success("Contrato encaminhado novamente para o investidor.")
    }).catch(error => {
      toast.error(error?.response?.data?.message || 'Não conseguimos encaminhar o contrato para o investidor.')
    }).finally(() => setLoading(false))
  }

  const getAditives = () => {
    api.get(`/contract/${contract.idContrato}/addendum`).then(resp => {
      setAditives(resp.data.addendums)
    }).catch(error => {
      toast.error(error?.response?.data?.message || 'Não conseguimos carregar os aditivos do contrato.')
    })
  }
  
  useEffect(() => {
    if(aditive === false) {
      getAditives()
    }
  }, [aditive])

  return (
    <div className=''>
      <div className={`w-full text-white overflow-auto`}>
        <div className='flex items-center'>
          <div className='w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center'>
            <DocumentTextIcon color='#000' width={20} />
          </div>
          <div className='gap-y-1 flex flex-col'>
            <p className='font-bold text-xs'>Detalhes do Contrato</p>
          </div>
        </div>
        <div className="w-full flex flex-wrap mt-4 gap-4 justify-start">
          <div className={`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${step === 0 ? 'bg-zinc-800 text-[#FF9900]' : ''}`} onClick={() => setStep(0)}>
            <p className="md:text-sm text-xs">Dados do Contrato</p>
          </div>
          <div className={`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${step === 1 ? 'bg-zinc-800 text-[#FF9900]' : ''}`} onClick={() => setStep(1)}>
            <p className="md:text-sm text-xs">Aditivos</p>
          </div>
        </div>
        {
          step === 0 ? <ContractData contract={contract} loading={loading} resendContract={resendContract} setAditive={setAditive} setModal={setModal} setModalPayment={setModalPayment} setRenew={setRenew} />
          : <div className="min-h-[300px]">
            <AditiveData contract={contract} aditives={aditives} getAditives={getAditives} />
          </div>
        }

      </div>

      <div className='w-full flex mt-10 gap-2 justify-end'>
          {
            contract.statusContrato === ContractStatus.AWAITING_AUDIT && (
              <Button size="lg" onClick={() => {
                navigation(`/contratos/contrato/${contract.idContrato}`)
              }}>
                Auditar contrato
              </Button>
            )
          }
        </div>

      {
        aditive && contract && (
          <AditiveContract contract={contract} setOpenModal={setAditive} />
        )
      }
    </div>
  )
}