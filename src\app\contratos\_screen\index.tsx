"use client";

import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { useEffect, useState } from "react";
import api from "@/core/api";
import {
  ArrowRightIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/react/24/outline";
import InputSearch from "@/components/InputSearch";
import { useRouter, useSearchParams } from "next/navigation";
import Contact from "@/models/contract";
import { clearLetters, cnpjMask, cpfMask } from "@/utils/masks";
import formatDate from "@/functions/formatDate";
import RenewContract from "../compose/RenewContract";
import ModalContract from "../compose/ModalContract";
import AddPayment from "../compose/AddPayment";
import { formatStatusContract } from "@/functions/formatStatus";
import StatusWithDescription from "@/components/StatusWithDescription";
import { getUserProfile } from "@/functions/getUserData";
import TableFormat from "@/components/Table/components/TableFormat";
import Table from "@/components/Table";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/utils/queries";
import { useDebounce } from "@uidotdev/usehooks";
import FilterModal from "../compose/FilterModal";
import Modal from "@/components/Modal";
import { SheetContent, SheetHeader, SheetTitle, SheetTrigger, Sheet } from "@/components/ui/sheet";

interface ContractsApiResponse {
  data: {
    documentos: Contact[];
    totalPaginas: number;
    total: number;
    [key: string]: any;
  };
}

interface ScreenProps {
  initialSignatarie: string;
  initialPage?: string;
  initialType?: string;
  initialStartData?: string;
  initialEndData?: string;
  initialStatus?: string;
}

export function Screen({
  initialSignatarie,
  initialPage = "1",
  initialType = "all",
  initialStartData = "",
  initialEndData = "",
  initialStatus = "Todos",
}: ScreenProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [contract, setContract] = useState<Contact>();
  const [modalFilter, setModalFilter] = useState<boolean>(false);
  const [modal, setModal] = useState<boolean>(false);
  const [modalPayment, setModalPayment] = useState<boolean>(false);
  const [renew, setRenew] = useState<boolean>(false);
  const [optSelected, setOptSelected] = useState(initialStatus);
  const [page, setPage] = useState<number>(Number(initialPage) || 1);
  const [signatarie, setSignatarie] = useState<string>(initialSignatarie || "");
  const debouncedSignatarie = useDebounce(signatarie, 300);
  const [filterData, setFilterData] = useState({
    startData: initialStartData,
    endData: initialEndData,
    type: initialType,
    status: initialStatus,
  });

  const [modalCreateContract, setModalCreateContract] = useState(false);

  const userProfile = getUserProfile();

  const { data: queryData, isLoading } = useQuery<ContractsApiResponse>({
    queryKey: QUERY_KEYS.CONTRACTS(
      page,
      debouncedSignatarie,
      optSelected,
      filterData
    ),
    queryFn: async (): Promise<ContractsApiResponse> => {
      console.log("API call dateTo:", filterData.endData);
      const response = await api.get(returnRoute(), {
        params: {
          roleId: userProfile.roleId,
          limit: "10",
          page,
          status: optSelected === "Todos" ? undefined : optSelected,
          signatarie: debouncedSignatarie
            ? clearLetters(debouncedSignatarie)
            : undefined,
          dateFrom:
            filterData.startData === "" ? undefined : filterData.startData,
          dateTo: filterData.endData === "" ? undefined : filterData.endData,
          contractType: filterData.type === "all" ? undefined : filterData.type,
        },
      });

      const totalPages = Number(response.data?.totalPaginas) || 1;
      if (page > totalPages) {
        setPage(1);
        return {
          data: { documentos: [], totalPaginas: totalPages, total: 0 },
        } as ContractsApiResponse;
      }

      return response;
    },
    staleTime: 60 * 1000, // 1 minute
  });

  useEffect(() => {
    localStorage.setItem("typeCreateContract", "");
    if (initialSignatarie) {
      const params = new URLSearchParams(searchParams.toString());
      params.set("signatarie", initialSignatarie);
      params.set("page", "1");
      router.replace(`?${params.toString()}`);
    }
  }, [initialSignatarie, router, searchParams]);

  const returnRoute = () => {
    switch (userProfile.name) {
      case "admin":
        return "/admin/list-contracts";
      case "superadmin":
        return "/contract/list-contracts/superadmin";
      default:
        return "";
    }
  };

  const translateTag = (tag: string) => {
    if (tag?.toUpperCase() === "P2P") {
      return "MUTUO";
    } else {
      return tag.toUpperCase();
    }
  };

  // Handle search to sync state and URL
  const handleSearch = (
    newSignatarie: string,
    filters = { ...filterData, status: optSelected }
  ) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("signatarie", newSignatarie);
    params.set("page", "1");
    params.set("type", filters.type || "all");
    params.set("startData", filters.startData || "");
    params.set("endData", filters.endData || "");
    params.set("status", filters.status || "Todos");
    console.log("handleSearch params:", params.toString());
    router.replace(`?${params.toString()}`);
  };

  // Handle signatarie change to sync state and URL
  const handleSignatarieChange = (newValue: string) => {
    setSignatarie(newValue);
    const params = new URLSearchParams(searchParams.toString());
    if (newValue === "") {
      params.delete("signatarie");
    } else {
      params.set("signatarie", newValue);
    }
    params.set("page", "1");
    router.replace(`?${params.toString()}`);
  };

  // Handle page change to sync state and URL
  const handleSetPage = (newPage: number) => {
    setPage(newPage);
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", String(newPage));
    router.replace(`?${params.toString()}`);
  };

  return (
    <div>
      <Sheet>
      <Header />
      <Sidebar>
        <>
        
          <div className="w-full text-white flex flex-col flex-wrap gap-2">
            <h1 className="m-auto font-bold text-2xl">Contratos</h1>
          </div>
          
          <TableFormat>
            <div className="w-full p-2 gap-4">
              <div className="flex flex-1 w-full justify-between items-center gap-4 mb-2">
                {(userProfile.name === "advisor" ||
                  userProfile.name === "broker") && (
                  <div
                    className="w-32 h-10 bg-orange-linear px-10 flex items-center justify-center md:mr-5 mb-2 md:mb-0 rounded-lg cursor-pointer"
                    onClick={() => {
                      if (userProfile.name === "advisor") {
                        localStorage.setItem("typeCreateContract", "broker");
                        router.push("/meus-contratos/registro-manual");
                      } else {
                        setModalCreateContract(true);
                      }
                    }}
                  >
                    <p>Criar</p>
                  </div>
                )}
                <div className="flex items-center gap-4 w-full justify-end">
                  <div className="md:w-3/12">
                    <InputSearch
                      isDocument
                      handleSearch={() => handleSearch(signatarie)}
                      setValue={handleSignatarieChange}
                      placeholder="Pesquisar por CPF/CNPJ"
                      value={signatarie}
                    />
                  </div>
                  <FilterModal
                    activeModal={modalFilter}
                    setActiveModal={setModalFilter}
                    filterData={{
                      ...filterData,
                      status: optSelected,
                    }}
                    setFilterData={(data) => {
                      setFilterData({
                        startData: data.startData,
                        endData: data.endData,
                        type: data.type,
                        status: data.status,
                      });
                      setOptSelected(data.status);
                    }}
                    handleSearch={handleSearch}
                    setPage={setPage}
                    signatarie={signatarie}
                  />
                </div>
              </div>
              <Table
                data={(queryData?.data?.documentos || []) as Contact[]}
                headers={[
                  {
                    title: "",
                    component: "id",
                    width: "30px",
                    render: (_, row) => (
                      <div
                        className="cursor-pointer"
                        onClick={() => {
                          setModal(true);
                          setContract(row as Contact);
                        }}
                      >
                        <SheetTrigger>
                          <ArrowTopRightOnSquareIcon color={"#fff"} width={20} />
                        </SheetTrigger>
                      </div>
                    ),
                  },
                  {
                    title: "Investidor",
                    component: "nomeInvestidor",
                    width: "150px",
                  },
                  {
                    title: "CPF/CNPJ",
                    component: "document",
                    position: "center",
                    width: "150px",
                    render: (_, row) => (
                      <p className="text-center">
                        {clearLetters(row.documentoInvestidor || "").length <=
                        11
                          ? cpfMask(row.documentoInvestidor || "")
                          : cnpjMask(row.documentoInvestidor || "")}
                      </p>
                    ),
                  },
                  {
                    title: "Valor",
                    component: "valorInvestimento",
                    position: "center",
                    render: (item) => (
                      <p className="text-center">
                        {Number(item || 0).toLocaleString("pt-br", {
                          style: "currency",
                          currency: "BRL",
                        })}
                      </p>
                    ),
                  },
                  {
                    title: "Rendimento",
                    component: "rendimentoInvestimento",
                    position: "center",
                    render: (item) => (
                      <p className="text-center">{String(item) || "0"}%</p>
                    ),
                  },
                  {
                    title: "Consultor",
                    component: "consultorResponsavel",
                    position: "center",
                    width: "100px",
                  },
                  {
                    title: "Criado em",
                    component: "inicioContrato",
                    position: "center",
                    render: (item) => (
                      <p className="text-center">{formatDate(String(item))}</p>
                    ),
                  },
                  {
                    title: "Status",
                    component: "statusContrato",
                    position: "center",
                    width: "100px",
                    render: (_, row) => (
                      <StatusWithDescription
                        description={
                          formatStatusContract(row.statusContrato).description
                        }
                        text={formatStatusContract(row.statusContrato).title}
                        textColor={
                          formatStatusContract(row.statusContrato).textColor
                        }
                      />
                    ),
                  },
                  {
                    title: "Modelo",
                    component: "inicioContrato",
                    position: "center",
                    render: (item, row) => (
                      <div className="px-2">
                        <div className="bg-white py-[5px] px-[10px] rounded-md text-center">
                          <p className="text-xs text-[#FF9900] font-bold">
                            {translateTag(row.tags) || "NE"}
                          </p>
                        </div>
                      </div>
                    ),
                  },
                ]}
                loading={isLoading}
                pagination={{
                  page,
                  lastPage: Number(queryData?.data?.totalPaginas) || 1,
                  perPage: 10,
                  setPage: handleSetPage,
                  totalItems: String(queryData?.data?.total || 0),
                }}
              />
            </div>
          </TableFormat>
          
            <SheetContent className="w-full md:w-[500px]">
              <ModalContract
                contract={contract}
                setRenew={setRenew}
                setModal={setModal}
                setModalPayment={setModalPayment}
              />
            </SheetContent>
          
          {modalPayment && contract && (
            <AddPayment
              contract={contract}
              setOpenModal={setModalPayment}
              documentSearch={signatarie}
              getContracts={handleSearch}
            />
          )}
          {renew && contract && (
            <RenewContract contract={contract} setOpenModal={setRenew} />
          )}
          {modalCreateContract && (
            <div className="fixed z-40 top-0 left-0 w-full h-full bg-[#3A3A3AAB]">
              <div className="absolute w-3/6 bg-[#1C1C1C] z-50 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] border border-[#FF9900] rounded-lg">
                <div className="p-4 text-white flex justify-between items-center border-b border-[#FF9900]">
                  <div>
                    <h3 className="font-bold text-base mb-1">
                      Novo Contrato Individual
                    </h3>
                    <p className="text-xs w-7/12">
                      Clique aqui para criar um novo contrato com negociação
                      exclusiva para você.
                    </p>
                  </div>
                  <div
                    className="bg-orange-linear p-2 rounded-full cursor-pointer animate-moveXRight"
                    onClick={() => {
                      localStorage.setItem("typeCreateContract", "broker");
                      router.push("/meus-contratos/registro-manual");
                    }}
                  >
                    <ArrowRightIcon width={20} />
                  </div>
                </div>
                <div className="p-4 text-white flex justify-between items-center">
                  <div>
                    <h3 className="font-bold text-base mb-1">
                      Novo Contrato Compartilhado
                    </h3>
                    <p className="text-xs w-7/12">
                      Clique aqui para criar um novo contrato com a negociação
                      personalizada de múltiplos assessores.
                    </p>
                  </div>
                  <div
                    className="bg-orange-linear p-2 rounded-full cursor-pointer"
                    onClick={() => {
                      localStorage.setItem("typeCreateContract", "advisors");
                      router.push("/meus-contratos/registro-manual");
                    }}
                  >
                    <ArrowRightIcon width={20} />
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      </Sidebar>
      </Sheet>
    </div>
  );
}
