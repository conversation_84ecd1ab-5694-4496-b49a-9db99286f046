import Button from "@/components/Button/Button";
import CoverForm from "@/components/CoverForm";
import Dropzone from "@/components/Dropzone";
import InputSelect from "@/components/Inputs/InputSelect";
import InputText from "@/components/Inputs/InputText";
import InputTextArea from "@/components/Inputs/InputTextArea";
import { signIca } from "@/constants/signIca";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import Contact from "@/models/contract";
import formatNumberValue from "@/utils/formatNumberValue";
import { clearLetters, cpfMask, valueMask } from "@/utils/masks";
import { createAdditive, CreateAdditiveForm } from "@/utils/schemas/createAdditive";
import { yupResolver } from "@hookform/resolvers/yup";
import moment from "moment-timezone";
import { use, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";

interface IProps {
  contract: Contact
  setOpenModal: (d: any) => void
}

export default function AditiveContract({ contract, setOpenModal }: IProps) {
  const [payment, setPayment] = useState<FileList>()
  const [newContract, setNewContract] = useState<FileList>()
  const [type, setType] = useState<'new' | 'exist'>('new')
  const [loading, setLoading] = useState(false)
  const [contractName, setContractName] = useState<string>() 
  const [paymentName, setPaymentName] = useState<string>()
  const modalRef = useRef<HTMLDivElement>(null)
  const {
      register,
      handleSubmit,
      watch,
      setValue,
      reset,
      formState: { errors },
    } = useForm({
      resolver: yupResolver(createAdditive),
      mode: "onChange"
    })
    

  useEffect(()=> {
    const handClickOutside = (event: MouseEvent) => {
      fi
    }
  })
  
  
  const onSubmit = (data: CreateAdditiveForm) => {
    if(type === 'new') {
      createAditive(data)
    } else {
      registerAditiveExists(data)
    }
  }

  const createAditive = (data: CreateAdditiveForm) => {
    setLoading(true)

    const payload = {
      contractId: contract?.idContrato,
      investment: {
        value: formatNumberValue(data.value),
        profile: data.profile,
        yield: Number(contract.rendimentoInvestimento),
        date: moment(data.initDate).format('YYYY-MM-DD')
      },
      accountBank: {
        bank: data.bank,
        accountNumber: data.accountNumber,
        agency: data.agency,
        pix: data.pix
      },
      owner: contract.documentoInvestidor.length > 11 ? {
        name: data.name,
        cpf: clearLetters(data.document || "")
      } : undefined,
      observations: data.observations,
      signIca: signIca
    }

    api.post('/contract/additive', payload).then(resp => {
      toast.success('Contrato de aditivo criado com sucesso!')
      setOpenModal(false)
    }).catch(error => {
      returnError(error, 'Não conseguimos criar o contrato de aditivo!')
    }).finally(() => setLoading(false))
  }

  const registerAditiveExists = (data: CreateAdditiveForm) => {
    if((type === 'exist') && (!newContract || !payment)) {
      return toast.error('É necessário anexar o contrato e o comprovante de pagamento')
    }

    if (type === 'exist') {
      if (moment.utc(data.initDate).isSameOrAfter(moment.utc(), 'day')) {
        return toast.warn('Data de início do contrato não pode ser igual ou maior que a data atual');
      }
    }

    setLoading(true)
    const form = new FormData()

    if(newContract) {
      form.append('contractPdf', newContract[0])
    }

    if(payment) {
      form.append('proofPayment', payment[0])
    }

    form.append('contractId', contract?.idContrato)
    form.append('investment[value]', data.value.replace('.', '').replace(',', '.'))
    form.append('investment[date]', moment(String(data.initDate)).format('YYYY-MM-DD'))
    form.append('investment[yield]', contract.rendimentoInvestimento)
    form.append('accountBank[bank]', data.bank)
    form.append('accountBank[accountNumber]', data.accountNumber)
    form.append('accountBank[agency]', data.agency)
    form.append('accountBank[pix]', data.pix)
    form.append('observations', String(data.observations))

    api.post('/contract/additive-manual', form).then(resp => {
      toast.success('Contrato de aditivo cadastrado com sucesso!')
      setOpenModal(false)
    }).catch(err => {
      returnError(err, 'Não foi possível cadastrar o aditivo')
    }).finally(() => setLoading(false))
  }

  useEffect(() => {
    if(contract.documentoInvestidor.length > 11) {
      setValue("isPF", false)
    } else {
      setValue("isPF", true)
    }
  }, [])

  useEffect(() => {
    if(type !== 'exist'){
      setValue('initDate', moment().format('YYYY-MM-DD'))
    } else {
      setValue('initDate', '')
    }
  }, [type])

  const handleRemoveFile = () => {
    setNewContract(undefined);
  }

  const handleRemovePayment = () => {
    setPayment(undefined);
  }

  return (
    <div className="fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-20">
      <div className="md:w-6/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900] overflow-auto h-[88%]">
        <p className="text-2xl font-bold">Criar contrato aditivo</p>

        <div className="mt-5 w-full">
          <div className="mb-5">
            <div className="flex gap-4 mb-5">
              <div>
                <input type="checkbox" name="" checked={type === 'new'} onChange={() => setType('new')} id="novo" className="mr-2 cursor-pointer" />
                <label htmlFor="novo" className="cursor-pointer select-none">Criar novo aditivo</label>
              </div>
              <div>
                <input type="checkbox" name="" checked={type === 'exist'} onChange={() => setType('exist')} id="manual" className="mr-2 cursor-pointer" />
                <label htmlFor="manual" className="cursor-pointer select-none">Cadastrar aditivo existente</label>
              </div>
            </div>

            <form action="" onSubmit={handleSubmit(onSubmit)}>
              {
                contract.documentoInvestidor.length > 11 && (
                  <CoverForm color="black" title="Dados do Representante Legal">
                    <div className="flex items-end gap-4 flex-wrap">
                      <InputText register={register} name="name" width="300px" error={!!errors.name} errorMessage={errors?.name?.message} label="Nome completo" />
                      <InputText register={register} name="document" width="200px" error={!!errors.document} errorMessage={errors?.document?.message} label="CPF" setValue={(e) => setValue('document', cpfMask(e || ''))} />
                    </div>
                  </CoverForm>
                )
              }
              <CoverForm color="black" title="Dados de Investimento">
                <div className="flex items-end gap-4 flex-wrap">
                  <InputText register={register} name="value" width="200px" error={!!errors.value} errorMessage={errors?.value?.message} label="Valor" setValue={(e) => setValue('value', valueMask(e || ''))} />
                  <InputSelect width="200px" name="profile" register={register} options={[
                    {label: 'Conservador', value: 'conservative'}, 
                    {label: 'Moderado', value: 'moderate'},
                    {label: 'Agressivo', value: 'aggressive'},
                  ]} error={!!errors.profile} errorMessage={errors?.profile?.message} label="Perfil" />
                  <InputText type="date" register={register}
                    minDate={type !== 'exist' ? moment.utc().format('YYYY-MM-DD') : undefined}
                    maxDate={type === 'exist' ? moment.utc().subtract(1, 'day').format('YYYY-MM-DD') : undefined}
                    disabled={type !== 'exist'}
                    name="initDate" 
                    width="200px" 
                    error={!!errors.initDate} errorMessage={errors?.initDate?.message} label="Inicio do contrato" />
                </div>
              </CoverForm>
              <CoverForm color="black" title="Dados bancarios">
                <div className="flex items-end gap-4 flex-wrap">
                  <InputText register={register} name="bank" width="200px" error={!!errors.bank} errorMessage={errors?.bank?.message} label="Banco" />
                  <InputText register={register} name="agency" width="200px" error={!!errors.agency} errorMessage={errors?.agency?.message} label="Agência" />
                  <InputText register={register} name="accountNumber" width="200px" error={!!errors.accountNumber} errorMessage={errors?.accountNumber?.message} label="Conta" />
                  <InputText register={register} name="pix" width="200px" error={!!errors.pix} errorMessage={errors?.pix?.message} label="Pix" />
                </div>
              </CoverForm>
              <CoverForm color="black" title="Observações">
                <InputTextArea name="observations" register={register} />
              </CoverForm>
              <div className='flex mt-10 gap-4 justify-end'>
                <div >
                  <Button 
                    label="Criar aditivo"
                    loading={loading}
                    className="bg-orange-linear"
                    disabled={loading}
                  />
                </div>
                <div >
                  <Button 
                    label="Fechar"
                    loading={false}
                    handleSubmit={() => setOpenModal(false)}
                  />
                </div>
              </div>
            </form>
          </div>
        </div>
        {
          type === 'exist' && (
            <div className='md:flex-row flex flex-col gap-2 mb-10 text-white jus  mt-5'>
              <div>
                <p className='mb-1'>Aditivo</p>
                <Dropzone 
                onFileUploaded={setNewContract} 
                fileName={contractName}
                onRemoveFile={handleRemoveFile}
                />
              </div>
              <div>
                <p className='mb-1'>Comprovante de pagamento</p>
                <Dropzone 
                onFileUploaded={setPayment} 
                fileName={paymentName}
                onRemoveFile={handleRemovePayment}
                />
              </div>
            </div>
          )
        }
      </div>
    </div>
  )
}
