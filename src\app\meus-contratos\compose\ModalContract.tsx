import Button from "@/components/Button/Button";
import api from "@/core/api";
import Contact, { Addendum } from "@/models/contract";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import ContractData from "./ContractData";
import AditiveContract from "./AditiveModal";
import AditiveData from "./AditiveData";
import { getUserProfile } from "@/functions/getUserData";
import { ContractStatus } from "@/functions/formatStatus";
import { useNavigation } from "@/hooks/navigation";
import moment from "moment";

interface IProps {
  contract: Contact
  setRenew: (d:any) => void
  setModal: (d:any) => void
  setModalPayment: (d:any) => void
}

export default function ModalContract({ contract, setModal, setRenew, setModalPayment }: IProps) {
  const [step, setStep] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [aditives, setAditives] = useState<Addendum[]>([])
  const [aditive, setAditive] = useState<boolean>(false)
  const { navigation } = useNavigation()

  const userProfile = getUserProfile()

  const resendContract = () => {
    setLoading(true)
    api.post(`/contract/send-notification/${contract.idContrato}`).then(resp => {
      toast.success("Contrato encaminhado novamente para o investidor.")
    }).catch(error => {
      toast.error(error?.response?.data?.message || 'Não conseguimos encaminhar o contrato para o investidor.')
    }).finally(() => setLoading(false))
  }

  const getAditives = () => {
    api.get(`/contract/${contract.idContrato}/addendum`).then(resp => {
      setAditives(resp.data.addendums)
    }).catch(error => {
      toast.error(error?.response?.data?.message || 'Não conseguimos carregar os aditivos do contrato.')
    })
  }

  console.log(moment.utc('2026-02-22').isBefore(moment.utc()))

  
  useEffect(() => {
    if(aditive === false) {
      getAditives()
    }
  }, [aditive])

  function validateResendContractWithStatus() {  
    if(
      contract.statusContrato === ContractStatus.AWAITING_INVESTOR_SIGNATURE ||
      contract.statusContrato === ContractStatus.SIGNATURE_SENT
    ) {
      return true
    }

    return false
  }

  return (
    <div className='z-10 fixed top-0 left-0 w-screen min-h-screen bg-[#1c1c1c71]'>
      <div className={`z-20 ${step > 0 ? 'md:w-8/12' : 'md:w-5/12'} w-full bg-[#1C1C1C] min-h-screen max-h-screen fixed top-0 right-0 border-l border-t border-b border-[#FF9900] p-10 text-white overflow-auto`}>
        <div className='flex items-center'>
          <div className='w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center'>
            <DocumentTextIcon color='#000' width={20} />
          </div>
          <div className='gap-y-1 flex flex-col'>
            <p className='font-bold text-xs'>Detalhes do Contrato</p>
          </div>
        </div>
        <div className="w-full flex flex-wrap mt-4 gap-4 justify-start">
          <div className={`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${step === 0 ? 'bg-zinc-800 text-[#FF9900]' : ''}`} onClick={() => setStep(0)}>
            <p className="md:text-sm text-xs">Dados do Contrato</p>
          </div>
          <div className={`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${step === 1 ? 'bg-zinc-800 text-[#FF9900]' : ''}`} onClick={() => setStep(1)}>
            <p className="md:text-sm text-xs">Aditivos</p>
          </div>
        </div>
        {
          step === 0 ? <ContractData contract={contract} loading={loading} resendContract={resendContract} setAditive={setAditive} setModal={setModal} setModalPayment={setModalPayment} setRenew={setRenew} />
          : <AditiveData contract={contract} aditives={aditives} getAditives={getAditives} />
        }

        <div className='w-full flex mt-10 flex-wrap gap-2 justify-end'>
          {
            (contract?.statusContrato === ContractStatus.EXPIRED && userProfile.name !== 'superadmin') && (
              <div>
                <Button 
                  label="Renovar contrato"
                  loading={false}
                  handleSubmit={() => setRenew(true)}
                />
              </div>
            )
          }
          {
            (!contract?.comprovamentePagamento && contract.statusContrato === ContractStatus.AWAITING_DEPOSIT) && (
              <div>
                <Button 
                  label="Anexar comprovante"
                  loading={false}
                  handleSubmit={() => setModalPayment(true)}
                  className="bg-orange-linear"
                />
              </div>
            )
          }
          <div>
            {
              (userProfile.name !== 'superadmin' && validateResendContractWithStatus()) && (
                <Button 
                  label="Reenviar Contrato"
                  loading={loading}
                  handleSubmit={resendContract}
                  className="bg-orange-linear"
                />
              )
            }
          </div>
          {
            (contract.statusContrato === ContractStatus.ACTIVE && !moment.utc(contract.fimContrato).isBefore(moment(), 'day')) && (
              <div>
                <Button
                  label="Criar Aditivo"
                  loading={false}
                  className="bg-orange-linear"
                  handleSubmit={() => {
                    setAditive(true)
                  }}
                />
              </div>
            )
          }
          {
            contract.statusContrato === ContractStatus.REJECTED_BY_AUDIT && (
              <div>
                <Button
                  label="Editar contrato"
                  loading={false}
                  className="bg-orange-linear"
                  handleSubmit={() => {
                    navigation(`/meus-contratos/contrato/${contract.idContrato}`)
                  }}
                />
              </div>
            )
          }
          <div>
            <Button 
              label="Fechar"
              loading={false}
              variant="secondary"
              handleSubmit={() => {
                setModal(false)
              }}
            />
          </div>
        </div>
      </div>

      {
        aditive && contract && (
          <AditiveContract contract={contract} setOpenModal={setAditive} />
        )
      }
    </div>
  )
}