import * as yup from 'yup'
import { clearLetters } from '../masks'
import { isValidateCNPJ, isValidateCPF } from '../validate-documents'
import validatePhoneNumber from '../validatePhoneNumber'
import isValidUF from '../isValidUf'
import { isUnderage } from '../isUnderage'

export const createPFContract = yup
  .object()
  .shape({
    isSCP: yup.boolean(),
    name: yup
      .string()
      .min(3, 'O nome deve ter no mínimo 3 caracteres')
      .required('Campo obrigatório'),
    document: yup.string()
      .test('valid-document','CPF inválido', value => {
        if (!value) return false
        return isValidateCPF(clearLetters(value || ''))
      })
      .required('Campo obrigatório'),
    email: yup
      .string()
      .email('E-mail incorreto')
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, 'E-mail inválido')
      .required('Campo obrigatório'),
    rg: yup
      .string()
      .min(3, 'RG deve ter no mínimo 3 caracteres')
      .required('Campo obrigatório'),
    phoneNumber: yup
      .string()
      .max(15, 'Número de telefone inválido!')
      .test('test-phone', 'Número de telefone inválido', value => {
        if(!value) return false
        return validatePhoneNumber(clearLetters(value))
      })
      .required('Campo obrigatório'),
    dtBirth: yup.string()
      .test('dtBirth-error', 'O investidor não pode ser menor de idade.', value => {
        if (!value) return false
        return !isUnderage(value)
      })
      .required('Campo obrigatório'),
    motherName: yup
      .string()
      .min(3, 'Nome da mãe deve conter no mínimo 3 caracteres')
      .required('Campo obrigatório'),

    // Endereço
    zipCode: yup.string().required('Obrigatório'),
    neighborhood: yup.string().required('Obrigatório'),
    state: yup
      .string()
      .min(2, 'Estado inválido!')
      .max(2, 'Estado inválido!')
      .test('state-error','Estado inválido!', value => {
        if(!value) return false
        return isValidUF(value)
      })
      .required('Obrigatório'),
    city: yup.string().required('Obrigatório'),
    complement: yup.string().default('').notRequired(),
    number: yup.string().required('Obrigatório'),
    street: yup.string().required('Obrigatório'),

    // Investimento
    value: yup.string().required('Obrigatório'),
    term: yup.string().required('Obrigatório'),
    yield: yup.string()
      .test('yield-error', 'Taxa Remuneração Mensal inválida', value => {
        if(!value) return false
        const yeld = Number(value.replace(',', '.'))
        
        return yeld > 0 && yeld <= 5
      })
      .required('Obrigatório'),
    purchaseWith: yup.string().required('Obrigatório'),
    initDate: yup.string().required('Obrigatório'),
    endDate: yup.string().required('Obrigatório'),
    profile: yup.string().required('Obrigatório'),
    isDebenture: yup.string().required('Obrigatório'),

    // Dados bancarios
    bank: yup
      .string()
      .min(2, 'Nome do banco muito curto')
      .required('Obrigatório'),
    accountNumber: yup
      .string()
      .min(3, 'Conta inválida')
      .required('Obrigatório'),
    agency: yup.string().min(2, 'Agência inválida').required('Obrigatório'),
    pix: yup.string().required('Obrigatório'),

    observations: yup.string().notRequired(),

    // Conditional schemas
    issuer: yup.string().required('Campo obrigatório'),
    placeOfBirth: yup.string().required('Campo obrigatório'),
    occupation: yup.string().required('Campo obrigatório'),
    amountQuotes: yup.string().when('isSCP', (isSCP, schema) => {
      if (isSCP[0] === true) {
        return schema.required('Campo obrigatório')
      } else {
        return schema.notRequired()
      }
    })

    // // Testemunhas
    // testifyPrimaryName: yup.string().notRequired(),
    // testifyPrimaryCpf: yup.string().notRequired(),

    // testifySecondaryName: yup.string().notRequired(),
    // testifySecondaryCpf: yup.string().notRequired(),
  })
  .required()

export const createPJContract = yup
  .object()
  .shape({
    isSCP: yup.boolean(),

    // Dados Representante
    email: yup
      .string()
      .email('E-mail incorreto')
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, 'E-mail inválido')
      .required('Campo obrigatório'),
    phoneNumber: yup
      .string()
      .test('test-phone', 'Número de telefone inválido', value => {
        if(!value) return false
        return validatePhoneNumber(clearLetters(value))
      })
      .max(15, 'Número de telefone inválido!')
      .required('Campo obrigatório'), // (69) 99959-8313
    dtBirth: yup.string()
      .test('dtBirth-error', 'O representante não pode ser menor de idade.', value => {
        if (!value) return false
        return !isUnderage(value)
      })
      .required('Campo obrigatório'),
    rg: yup
      .string()
      .min(3, 'RG deve ter no mínimo 3 caracteres')
      .required('Obrigatório'),
    ownerName: yup
      .string()
      .min(3, 'O nome deve ter no mínimo 3 caracteres')
      .required('Campo obrigatório'),
    ownerDocument: yup
      .string()
      .test('valid-document','CPF inválido', value => {
        if (!value) return false
        return isValidateCPF(clearLetters(value || ''))
      })
      .min(3, 'O nome da mãe deve ter no mínimo 3 caracteres')
      .required('Campo obrigatório'),
    motherName: yup.string().required('Campo obrigatório'),
    issuer: yup.string().required('Campo obrigatório'),
    placeOfBirth: yup.string().required('Campo obrigatório'),
    occupation: yup.string().required('Campo obrigatório'),

    // Dados Compania
    name: yup
      .string()
      .min(3, 'O nome deve ter no mínimo 3 caracteres')
      .required('Campo obrigatório'),
    document: yup.string()
      .test('valid-document-business','CNPJ inválido', value => {
        if (!value) return false
        return isValidateCNPJ(clearLetters(value || ''))
      })
      .required('Campo obrigatório'),
    companyType: yup.string().required('Obrigatório'),
    // dtOpening: yup.string().required('Campo oobrigatório'),

    // Endereço Representante
    zipCode: yup.string().required('Obrigatório'),
    neighborhood: yup.string().required('Obrigatório'),
    state: yup
      .string()
      .min(2, 'Estado inválido!')
      .max(2, 'Estado inválido!')
      .test('state-error','Estado inválido!', value => {
        if(!value) return false
        return isValidUF(value)
      })
      .required('Obrigatório'),
    city: yup.string().required('Obrigatório'),
    complement: yup.string().default('').notRequired(),
    number: yup.string().required('Obrigatório'),
    street: yup.string().required('Obrigatório'),

    // Investimento
    value: yup.string().required('Obrigatório'),
    term: yup.string().required('Obrigatório'),
    yield: yup.string()
      .test('yield-error', 'Taxa Remuneração Mensal inválida', value => {
        if(!value) return false
        const yeld = Number(value.replace(',', '.'))
        
        return yeld > 0 && yeld <= 5
      })
      .required('Obrigatório'),
    purchaseWith: yup.string().required('Obrigatório'),
    initDate: yup.string().required('Obrigatório'),
    endDate: yup.string().required('Obrigatório'),
    profile: yup.string().required('Obrigatório'),
    isDebenture: yup.string().required('Obrigatório'),

    amountQuotes: yup.string().when('isSCP', (isSCP, schema) => {
      if (isSCP[0] === true) {
        return schema.required('Campo obrigatório')
      } else {
        return schema.notRequired()
      }
    }),

    // Dados bancarios
    bank: yup
      .string()
      .min(2, 'Nome do banco muito curto')
      .required('Obrigatório'),
    accountNumber: yup
      .string()
      .min(3, 'Conta inválida')
      .required('Obrigatório'),
    agency: yup.string().min(2, 'Agência inválida').required('Obrigatório'),
    pix: yup.string().required('Obrigatório'),

    observations: yup.string().notRequired(),

    // Endereço Compania
    companyNumber: yup.string().required('Campo obrigatório'),
    companyComplement: yup.string().default('').notRequired(),
    companyCity: yup.string().required('Campo obrigatório'),
    companyState: yup
      .string()
      .min(2, 'Estado inválido!')
      .max(2, 'Estado inválido!')
      .test('company-state-error','Estado inválido!', value => {
        if(!value) return false
        return isValidUF(value)
      })
      .required('Campo obrigatório'),
    companyStreet: yup.string().required('Campo obrigatório'),
    companyZipCode: yup.string().required('Campo obrigatório'),
    companyNeighborhood: yup.string().required('Campo obrigatório')

    // // Testemunhas
    // testifyPrimaryName: yup.string().notRequired(),
    // testifyPrimaryCpf: yup.string().notRequired(),

    // testifySecondaryName: yup.string().notRequired(),
    // testifySecondaryCpf: yup.string().notRequired(),
  })
  .required()
