import { MagnifyingGlassCircleIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline"

interface Iprops {
  placeholder?: string
  value: string
  setValue: (d:string) => void
  disabled?: boolean
  handleSearch: () => void,
  handleChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
}

export default function InputSearch({ setValue, value, disabled = false, handleSearch, placeholder = 'Pesquisar'}: Iprops) {
  return (
    <div className="w-full bg-white flex items-center justify-start p-1 rounded-full">
      <div onClick={handleSearch}>
        <MagnifyingGlassIcon width={20} color="#868E96" className="cursor-pointer" />
      </div>
      <input 
        type="text" 
        className="border-none w-full text-[#868E96] ml-2 focus:outline-none" 
        placeholder={placeholder}
        onKeyDown={(event) => {
          if(event.key === 'Enter') {
            handleSearch()
          }
        }}
        disabled={disabled}
        value={value}
        onChange={({target}) => setValue(target.value)}
      />
    </div>
  )
}