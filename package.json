{"name": "ica-invest-contracts", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-slot": "^1.2.2", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.76.1", "@uidotdev/usehooks": "^2.4.1", "ag-charts-community": "^10.3.0", "ag-charts-react": "^10.3.0", "axios": "^1.5.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "headlessui": "^0.0.0", "heroicons": "^2.0.18", "leaflet": "^1.9.4", "lottie-react": "^2.4.0", "lucide-react": "^0.510.0", "moment": "^2.29.4", "moment-timezone": "^0.5.47", "next": "13.5.4", "next-auth": "^4.24.11", "radix-ui": "^1.4.1", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.53.0", "react-leaflet": "^4.2.1", "react-toastify": "^9.1.3", "swr": "^2.3.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.74.7", "@types/crypto-js": "^4.2.2", "@types/leaflet": "^1.9.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10", "eslint": "^8", "eslint-config-next": "13.5.4", "postcss": "^8", "tailwindcss": "^3", "typescript": "^5"}}