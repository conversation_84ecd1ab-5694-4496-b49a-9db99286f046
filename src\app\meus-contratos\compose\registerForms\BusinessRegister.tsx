'use client'

import InputText from '@/components/Inputs/InputText'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm, SubmitHandler } from 'react-hook-form'
import { schemaRegister } from '@/utils/schemas/schemasValidation'
import Button from '@/components/Button/Button'
import {
  cepMask,
  clearLetters,
  cnpjMask,
  cpfMask,
  phoneMask,
  valueMask
} from '@/utils/masks'
import { createPJContract } from '@/utils/schemas/createContract'
import { useEffect, useState } from 'react'
import InputSelect from '@/components/Inputs/InputSelect'
import InputTextArea from '@/components/Inputs/InputTextArea'
import axios from 'axios'
import { UserProfile } from '@/models/user'
import { signIca } from '@/constants/signIca'
import api from '@/core/api'
import returnError from '@/functions/returnError'
import { toast } from 'react-toastify'
import SelectSearch from '@/components/SelectSearch'
import Input from '@/components/Input'
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline'
import CoverForm from '@/components/CoverForm'
import { getFinalDataWithMount } from '@/functions/getDataFilter'
import moment from 'moment'
import momentTz from 'moment-timezone'
import formatNumberValue from '@/utils/formatNumberValue'
import { isValidateCNPJ, isValidateCPF } from '@/utils/validate-documents'

import { isUnderage } from '@/utils/isUnderage'
import { getUserProfile } from '@/functions/getUserData'
import validatePhoneNumber from '@/utils/validatePhoneNumber'
import isValidUF from '@/utils/isValidUf'
import { getZipCode } from '@/functions/getZipCode'

interface IProps {
  modalityContract: 'MUTUO' | 'SCP'
}

interface AdvisorsTableItem {
  id: string
  taxValue: number | string
  generatedId: string
}

interface BrokerAcessor {
  name: string
  id: string
  rate: string
}

const companyTypes = [
  {
    label: 'MEI',
    value: 'MEI'
  },
  {
    label: 'EI',
    value: 'EI'
  },
  {
    label: 'EIRELI',
    value: 'EIRELI'
  },
  {
    label: 'SLU',
    value: 'SLU'
  },
  {
    label: 'LTDA',
    value: 'LTDA'
  },
  {
    label: 'SA',
    value: 'SA'
  },
  {
    label: 'SS',
    value: 'SS'
  },
  {
    label: 'CONSORCIO',
    value: 'CONSORCIO'
  }
]

export default function BusinessRegister({ modalityContract }: IProps) {
  const [confirm, setConfirm] = useState(false)
  const [loading, setLoading] = useState(false)
  const [zipCode, setZipCode] = useState('')
  const [companyZipCode, setCompanyZipCode] = useState('')
  const [startDate, setStartDate] = useState('')
  const [investDate, setInvestDate] = useState('')

  const [acessors, setAcessors] = useState<BrokerAcessor[]>([])
  const [loadingAcessors, setLoadingAcessors] = useState(false)
  const [advisors, setAdvisors] = useState<AdvisorsTableItem[]>([
    {
      generatedId: crypto.randomUUID(),
      id: '',
      taxValue: ''
    }
  ])

  const userProfile = getUserProfile()
  const typeCreate: string = String(localStorage.getItem('typeCreateContract'))

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isValid }
  } = useForm({
    resolver: yupResolver(createPJContract),
    mode: 'onChange'
  })

  const termData = watch('term')

  useEffect(() => {
    setInvestDate(termData)
  }, [termData])

  useEffect(() => {
    if (startDate && investDate) {
      const endData = getFinalDataWithMount({
        investDate,
        startDate
      })

      setValue('endDate', endData)
    }
  }, [startDate, investDate])

  function getAcessors() {
    setLoadingAcessors(true)
    api
      .get('/wallets/broker/advisors')
      .then(resp => {
        setAcessors(resp.data)
      })
      .catch(error => {
        returnError(error, 'Erro ao buscar a lista de brokers')
      })
      .finally(() => setLoadingAcessors(false))
  }

  useEffect(() => {
    if (userProfile.name === 'broker') {
      getAcessors()
    }
  }, [])

  useEffect(() => {
    setValue('isSCP', modalityContract === 'SCP')
  }, [modalityContract])

  const onSubmit = (data: any) => {
    if (!confirm)
      return toast.warn('Aceite os termos para liberar a criação do contrato!')

    if (!isValidateCNPJ(clearLetters(data.document || ''))) {
      return toast.warn('CNPJ da empresa inválido!')
    }

    if (!isValidateCPF(clearLetters(data.ownerDocument || ''))) {
      return toast.warn('CPF do representante inválido!')
    }

    if (isUnderage(data.dtBirth)) {
      return toast.warn('O representante não pode ser menor de idade.')
    }

    if (!validatePhoneNumber(data.phoneNumber)) {
      return toast.warn('Número de telefone inválido')
    }

    if (!isValidUF(data.state)) {
      return toast.warn('Estado do representante inválido')
    }

    if (!isValidUF(data.companyState)) {
      return toast.warn('Estado da empresa inválido')
    }

    const yeld = Number(data.yield.replace(',', '.'))

    if (yeld < 0 || yeld > 5) {
      return toast.warn('Taxa Remuneração Mensal inválida')
    }

    setLoading(true)

    const investmentBody = {
      amount: formatNumberValue(data.value),
      monthlyRate: Number(data.yield),
      durationInMonths: Number(data.term),
      paymentMethod: data.purchaseWith,
      endDate: moment(data.endDate, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      profile: data.profile,
      quotaQuantity:
        modalityContract === 'SCP' ? Number(data.amountQuotes) : undefined,
      isDebenture: data.debenture === 's'
    }

    const bankAccountBody = {
      bank: data.bank,
      agency: data.agency,
      account: data.accountNumber,
      pix: data.pix
    }

    const addressBody = {
      street: data.street,
      city: data.city,
      state: data.state,
      neighborhood: data.neighborhood,
      postalCode: clearLetters(data.zipCode),
      number: data.number,
      complement: data.complement
    }

    const individualBody = {
      fullName: data.ownerName,
      cpf: clearLetters(data.ownerDocument),
      rg: data.rg,
      issuingAgency: data.issuer,
      nationality: data.placeOfBirth,
      occupation: data.occupation,
      birthDate: data.dtBirth,
      email: data.email,
      phone: `55${clearLetters(data.phoneNumber)}`,
      motherName: data.motherName,
      address: addressBody
    }

    const companyAddressBody = {
      street: data.companyStreet,
      city: data.companyCity,
      state: data.companyState,
      neighborhood: data.companyNeighborhood,
      postalCode: clearLetters(data.companyZipCode),
      number: data.companyNumber,
      complement: data.companyComplement
    }

    const companyBody = {
      corporateName: data.name,
      cnpj: clearLetters(data.document),
      type: data.companyType,
      address: companyAddressBody,
      representative: individualBody
    }

    const newPayload = {
      personType: 'PJ',
      contractType: modalityContract,
      advisors:
        typeCreate === 'advisors'
          ? advisors.map(adv => ({
              advisorId: adv.id,
              rate: Number(String(adv.taxValue).replace(',', '.'))
            }))
          : [],
      investment: investmentBody,
      bankAccount: bankAccountBody,
      company: companyBody,
      role: userProfile.name
    }

    toast.info('Cadastrando investidor...')

    api
      .post('/contract/manual', newPayload)
      .then(resp => {
        const userTimeZone = momentTz.tz.guess()
        const now = momentTz().tz(userTimeZone)

        const hour = now.hour()
        const isHourValid = hour >= 9 && hour < 18

        if (isHourValid) {
          toast.success('Conta pré cadastrada com sucesso!')
        } else {
          toast.success(
            'Contrato criado com sucesso. Informamos que, por ter sido realizado fora do horário comercial, o registro contábil será processado somente no próximo dia útil.',
            {
              delay: 6000
            }
          )
        }
        reset()

        setValue('initDate', moment().format('YYYY-MM-DD'))
        setStartDate(moment().format('YYYY-MM-DD'))
      })
      .catch(error => {
        returnError(error, 'Tivemos um erro ao cadastrar a conta')
      })
      .finally(() => setLoading(false))
  }

  useEffect(() => {
    getCep(zipCode || '', 'person')
  }, [zipCode])

  useEffect(() => {
    getCep(companyZipCode || '', 'company')
  }, [companyZipCode])

  async function getCep(value: string, type: 'company' | 'person') {
    const response = await getZipCode(clearLetters(value))

    if (response !== null) {
      if (type === 'person') {
        setValue('neighborhood', response.neighborhood)
        setValue('city', response.city)
        setValue('state', response.state)
        setValue('street', response.street)
      } else {
        setValue('companyNeighborhood', response.neighborhood)
        setValue('companyStreet', response.street)
        setValue('companyCity', response.city)
        setValue('companyState', response.state)
      }
    }
  }

  const handleInputChange = (id: string, value: string) => {
    setAdvisors(prevItems =>
      prevItems.map(item =>
        item.generatedId === id ? { ...item, taxValue: value } : item
      )
    )
  }

  useEffect(() => {
    setValue('initDate', moment().format('YYYY-MM-DD'))
    setStartDate(moment().format('YYYY-MM-DD'))
  }, [])

  return (
    <div>
      <form
        action=""
        onSubmit={handleSubmit(onSubmit)}
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault() // Bloqueia o envio ao pressionar Enter
          }
        }}
      >
        <CoverForm title={'Dados Pessoais - Representante'}>
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="ownerName"
              width="300px"
              error={!!errors.ownerName}
              errorMessage={errors?.ownerName?.message}
              label="Nome"
            />
            <InputText
              register={register}
              name="ownerDocument"
              width="200px"
              error={!!errors.ownerDocument}
              errorMessage={errors?.ownerDocument?.message}
              label="CPF"
              setValue={e => setValue('ownerDocument', cpfMask(e || ''))}
            />
            <InputText
              register={register}
              name="rg"
              width="200px"
              error={!!errors.rg}
              errorMessage={errors?.rg?.message}
              label="RG"
            />
            <InputText
              register={register}
              name="issuer"
              width="200px"
              error={!!errors.issuer}
              errorMessage={errors?.issuer?.message}
              label="Orgão emissor"
            />
            <InputText
              register={register}
              name="placeOfBirth"
              width="200px"
              error={!!errors.placeOfBirth}
              errorMessage={errors?.placeOfBirth?.message}
              label="Nacionalidade"
            />
            <InputText
              register={register}
              name="occupation"
              width="200px"
              error={!!errors.occupation}
              errorMessage={errors?.occupation?.message}
              label="Ocupação"
            />
            <InputText
              register={register}
              name="motherName"
              width="250px"
              error={!!errors.motherName}
              errorMessage={errors?.motherName?.message}
              label="Nome da mãe"
            />
            <InputText
              type="date"
              register={register}
              name="dtBirth"
              width="200px"
              error={!!errors.dtBirth}
              errorMessage={errors?.dtBirth?.message}
              label="Data de Nascimento"
            />
            <InputText
              width="200px"
              register={register}
              name="phoneNumber"
              error={!!errors.phoneNumber}
              errorMessage={errors?.phoneNumber?.message}
              label="Celular"
              maxLength={15}
              setValue={e => setValue('phoneNumber', phoneMask(e || ''))}
            />
            <InputText
              register={register}
              name="email"
              width="300px"
              error={!!errors.email}
              errorMessage={errors?.email?.message}
              label="E-mail"
            />
            <InputText
              register={register}
              name="zipCode"
              width="200px"
              error={!!errors.zipCode}
              errorMessage={errors?.zipCode?.message}
              label="CEP"
              setValue={e => {
                setZipCode(e)
                setValue('zipCode', cepMask(e))
              }}
            />
            <InputText
              register={register}
              name="neighborhood"
              width="300px"
              error={!!errors.neighborhood}
              errorMessage={errors?.neighborhood?.message}
              label="Bairro"
            />
            <InputText
              register={register}
              name="street"
              width="300px"
              error={!!errors.street}
              errorMessage={errors?.street?.message}
              label="Rua"
            />

            <InputText
              register={register}
              name="city"
              width="200px"
              error={!!errors.city}
              errorMessage={errors?.city?.message}
              label="Cidade"
            />
            <InputText
              register={register}
              maxLength={2}
              setValue={e => setValue('state', String(e).toUpperCase())}
              name="state"
              width="150px"
              error={!!errors.state}
              errorMessage={errors?.state?.message}
              label="Estado"
            />
            <InputText
              register={register}
              name="number"
              width="200px"
              error={!!errors.number}
              errorMessage={errors?.number?.message}
              label="Número"
            />
            <InputText
              register={register}
              name="complement"
              width="200px"
              error={!!errors.complement}
              errorMessage={errors?.complement?.message}
              label="Complemento"
            />
          </div>
        </CoverForm>
        <CoverForm title="Dados da empresa">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="name"
              width="400px"
              error={!!errors.name}
              errorMessage={errors?.name?.message}
              label="Razão Social"
            />
            <InputText
              register={register}
              name="document"
              width="200px"
              error={!!errors.document}
              errorMessage={errors?.document?.message}
              label="CNPJ"
              setValue={e => setValue('document', cnpjMask(e || ''))}
            />
            {/* <InputText type="date" register={register} name="dtBirth" width="200px" error={!!errors.dtOpening} errorMessage={errors?.dtOpening?.message} label="Data de Abertura" /> */}
            <InputSelect
              width="200px"
              name="companyType"
              register={register}
              options={companyTypes}
              error={!!errors.companyType}
              errorMessage={errors?.companyType?.message}
              label="Tipo"
            />
            <InputText
              register={register}
              name="companyZipCode"
              width="200px"
              error={!!errors.companyZipCode}
              errorMessage={errors?.companyZipCode?.message}
              label="CEP"
              setValue={e => {
                setCompanyZipCode(e)
                setValue('companyZipCode', cepMask(e))
              }}
            />
            <InputText
              register={register}
              name="companyNeighborhood"
              width="300px"
              error={!!errors.companyNeighborhood}
              errorMessage={errors?.companyNeighborhood?.message}
              label="Bairro"
            />
            <InputText
              register={register}
              name="companyStreet"
              width="300px"
              error={!!errors.companyStreet}
              errorMessage={errors?.companyStreet?.message}
              label="Rua"
            />
            <InputText
              register={register}
              name="companyCity"
              width="200px"
              error={!!errors.companyCity}
              errorMessage={errors?.companyCity?.message}
              label="Cidade"
            />
            <InputText
              register={register}
              maxLength={2}
              setValue={e => setValue('companyState', String(e).toUpperCase())}
              name="companyState"
              width="150px"
              error={!!errors.companyState}
              errorMessage={errors?.companyState?.message}
              label="Estado"
            />
            <InputText
              register={register}
              name="companyNumber"
              width="200px"
              error={!!errors.companyNumber}
              errorMessage={errors?.companyNumber?.message}
              label="Número"
            />
            <InputText
              register={register}
              name="companyComplement"
              width="200px"
              error={!!errors.companyComplement}
              errorMessage={errors?.companyComplement?.message}
              label="Complemento"
            />
          </div>
        </CoverForm>
        <CoverForm title="Dados de Investimento">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="value"
              width="200px"
              error={!!errors.value}
              errorMessage={errors?.value?.message}
              label="Valor"
              setValue={e => setValue('value', valueMask(e || ''))}
            />
            <InputText
              register={register}
              type="text"
              name="term"
              width="250px"
              error={!!errors.term}
              errorMessage={errors?.term?.message}
              label="Prazo investimento - em meses"
              placeholder="ex: 12"
            />
            <InputText
              register={register}
              type="text"
              name="yield"
              width="250px"
              error={!!errors.yield}
              errorMessage={errors?.yield?.message}
              label="Taxa Remuneração Mensal - em %"
              placeholder="ex: 2,5"
            />
            <InputSelect
              width="200px"
              name="purchaseWith"
              register={register}
              options={[
                { label: 'PIX', value: 'pix' },
                { label: 'Boleto', value: 'boleto' },
                { label: 'Transferência', value: 'bank_transfer' }
              ]}
              error={!!errors?.purchaseWith}
              errorMessage={errors?.purchaseWith?.message}
              label="Comprar com"
            />
            {modalityContract === 'SCP' && (
              <>
                <InputText
                  register={register}
                  type="number"
                  name="amountQuotes"
                  width="150px"
                  error={!!errors.amountQuotes}
                  errorMessage={errors?.amountQuotes?.message}
                  label="Quantidade de cotas"
                />
              </>
            )}
            <InputText
              type="date"
              register={register}
              minDate={moment().format('YYYY-MM-DD')}
              disabled={true}
              name="initDate"
              width="200px"
              setValue={setStartDate}
              error={!!errors.initDate}
              errorMessage={errors?.initDate?.message}
              label="Inicio do contrato"
            />
            <InputText
              type="text"
              register={register}
              name="endDate"
              width="200px"
              error={!!errors.endDate}
              disabled={true}
              errorMessage={errors?.endDate?.message}
              label="Final do contrato"
            />
            <InputSelect
              width="200px"
              name="profile"
              register={register}
              options={[
                { label: 'Conservador', value: 'conservative' },
                { label: 'Moderado', value: 'moderate' },
                { label: 'Agressivo', value: 'aggressive' }
              ]}
              error={!!errors.profile}
              errorMessage={errors?.profile?.message}
              label="Perfil"
            />
            <InputSelect
              width="100px"
              name="isDebenture"
              register={register}
              options={[
                { label: 'Sim', value: 's' },
                { label: 'Não', value: 'n' }
              ]}
              error={!!errors.isDebenture}
              errorMessage={errors?.isDebenture?.message}
              label="Debênture"
            />
          </div>
        </CoverForm>
        {typeCreate === 'advisors' && (
          <CoverForm title="Adicionar Assessor">
            <div>
              {advisors.length > 0 ? (
                advisors?.map((advisor, i) => (
                  <div
                    key={i}
                    className="flex justify-between items-end gap-4 mb-4"
                  >
                    <div className="flex-1">
                      <SelectSearch
                        label="Selecione um Assessor"
                        items={acessors}
                        value={''}
                        setValue={() => {}}
                        loading={loadingAcessors}
                        handleChange={test => {
                          const advisorFilter = advisors.filter(
                            adv => adv.generatedId === advisor.generatedId
                          )[0]
                          const newAdvisor = {
                            generatedId: advisorFilter.generatedId,
                            id: test.id,
                            taxValue: Number(test.rate)
                          }
                          const newArray = advisors
                          newArray[i] = newAdvisor
                          setAdvisors([...newArray])
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <Input
                        label="Adicione a Taxa - em %"
                        id={`advisor-${i}`}
                        name=''
                        value={String(advisor.taxValue)}
                        type="text"
                        setValue={e =>
                          handleInputChange(advisor.generatedId, e)
                        }
                      />
                    </div>
                    <div
                      className="bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full"
                      onClick={() => {
                        const newArray = advisors.filter(
                          (_, index) => index !== i
                        )
                        setAdvisors(newArray)
                      }}
                    >
                      <TrashIcon width={20} />
                    </div>
                  </div>
                ))
              ) : (
                <div>
                  <p className="text-white">Nenhum assessor adicionado!</p>
                </div>
              )}
              <div
                className="bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5"
                onClick={() => {
                  setAdvisors([
                    ...advisors,
                    {
                      generatedId: crypto.randomUUID(),
                      id: '',
                      taxValue: ''
                    }
                  ])
                }}
              >
                <PlusIcon width={25} color="#fff" />
              </div>
            </div>
          </CoverForm>
        )}
        <CoverForm title="Dados bancarios">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              register={register}
              name="bank"
              width="300px"
              error={!!errors.bank}
              errorMessage={errors?.bank?.message}
              label="Banco"
            />
            <InputText
              register={register}
              name="agency"
              width="200px"
              error={!!errors.agency}
              errorMessage={errors?.agency?.message}
              label="Agência"
            />
            <InputText
              register={register}
              name="accountNumber"
              width="200px"
              error={!!errors.accountNumber}
              errorMessage={errors?.accountNumber?.message}
              label="Conta"
            />
            <InputText
              register={register}
              name="pix"
              width="250px"
              error={!!errors.pix}
              errorMessage={errors?.pix?.message}
              label="Pix"
            />
          </div>
        </CoverForm>
        {/* {
          modalityContract === 'SCP' && <div className="flex w-full justify-start items-center gap-4">
          <CoverForm title="Testemunha 1">
            <div className="flex flex-col gap-4">
              <InputText register={register} name="testifyPrimaryName" width="250px" error={!!errors.testifyPrimaryName} errorMessage={errors?.testifyPrimaryName?.message} label="Nome" />
              <InputText register={register} name="testifyPrimaryCpf" width="250px" setValue={(e) => setValue("testifyPrimaryCpf", cpfMask(e))} error={!!errors.testifyPrimaryCpf} errorMessage={errors?.testifyPrimaryCpf?.message} label="CPF" />
            </div>
          </CoverForm>
          <CoverForm title="Testemunha 2">
            <div className="flex flex-col gap-4">
              <InputText register={register} name="testifySecondaryName" width="250px" error={!!errors.testifySecondaryName} errorMessage={errors?.testifySecondaryName?.message} label="Nome" />
              <InputText register={register} name="testifySecondaryCpf" width="250px" setValue={(e) => setValue("testifySecondaryCpf", cpfMask(e))} error={!!errors.testifySecondaryCpf} errorMessage={errors?.testifySecondaryCpf?.message} label="CPF" />
            </div>
          </CoverForm>
        </div>
        } */}
        <CoverForm title="Observações">
          <InputTextArea name="observations" register={register} />
        </CoverForm>
        <CoverForm title="Dados para Depósito">
          <div className="m-auto border-none">
            <p className="text-center font-bold text-white">
              DADOS PARA DEPÓSITO
            </p>
            <p className="text-center text-white font-extralight">
              ICABANK SOLUÇÕES FINANCEIRAS LTDA
            </p>
            <p className="text-center text-white font-extralight">
              CNPJ: 37.468.454/0001-00
            </p>
            <p className="text-center text-white font-extralight">
              INSTITUIÇÃO 332 - Acesso Soluções de Pagamento S.A
            </p>
            <p className="text-center text-white font-extralight">
              AGÊNCIA: 0001 CONTA: 2269051-4
            </p>
            <p className="text-center text-white font-extralight">
              PIX: 37.468.454/0001-00
            </p>
          </div>
        </CoverForm>
        <div className="mb-5">
          <p className="text-white font-extralight text-sm">Declaro que:</p>
          <p className="text-white font-extralight text-sm">
            As informações contidas nesta ficha cadastral, de caráter
            confidencial, são exatas e de minha inteira responsabilidade,
            sujeitando-me, se inverídicas, às penas estabelecidas no Código
            Penal vigente:
          </p>
          <div className="flex mt-5">
            <input
              type="checkbox"
              checked={confirm}
              className="cursor-pointer"
              onChange={() => setConfirm(!confirm)}
            />
            <p className="text-white font-extralight text-sm ml-2">
              Li e aceito os TERMOS e CONDIÇÕES
            </p>
          </div>
        </div>
        <div className="md:w-52 mb-10">
          <Button
            label="Enviar"
            size="lg"
            loading={loading}
            disabled={!confirm || loading || !isValid}
          />
        </div>
      </form>
    </div>
  )
}
