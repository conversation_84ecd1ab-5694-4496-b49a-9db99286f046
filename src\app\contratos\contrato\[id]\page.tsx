"use client"

import { useEffect, useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import CoverForm from "@/components/CoverForm";
import PhysicalContract from "./compose/PhysicalContract";
import BusinessContract from "./compose/BusinessContract";
import { ContractPF, ContractPJ } from "../types";
import api from "@/core/api";
import { useParams } from "next/navigation";
import returnError from "@/functions/returnError";
import { getUserProfile } from "@/functions/getUserData";
import { toast } from "react-toastify";
import { useNavigation } from "@/hooks/navigation";
export default function CreateInvestor() {
  const { id } = useParams()
  const [typeAccount, setTypeAccount] = useState('pf')
  const [contractType, setContractType] = useState<'mutuo' | 'scp'>('mutuo')
  const [contractPJ, setContractPJ] = useState<ContractPJ>()
  const [contractPF, setContractPF] = useState<ContractPF>()
  const [isLoadingAuth, setIsLoadingAuth] = useState(true)
  const userProfile = getUserProfile()
  const { navigation } = useNavigation()

  useEffect(() => {
    // Simula um pequeno delay para verificar a autenticação
    const checkAuth = async () => {
      try {
        if(userProfile?.name !== 'superadmin') {
          toast.error('Você não tem permissão para acessar essa página')
          navigation('/meus-contratos')
          return
        }
        setIsLoadingAuth(false)
      } catch (error) {
        toast.error('Erro ao verificar permissões')
        navigation('/meus-contratos')
      }
    }

    checkAuth()
  }, [])

  const getContractData = () => {
    api
      .get(`/contract/get-detail/${id}`)
      .then((resp) => {
        setContractType(resp.data.investment.type);
        if (resp.data.investor.type === "business") {
          setContractPJ(resp.data);
          setTypeAccount("pj");
        } else {
          setContractPF(resp.data);
          setTypeAccount("pf");
        }
      })
      .catch((err) => {
        returnError(err, "Erro ao buscar dados do contrato.");
      });
  };

  useEffect(() => {
    getContractData();
  }, []);

  // Mostra loading enquanto verifica autenticação
  if (isLoadingAuth) {
    return (
      <>
        <Header/>
        <Sidebar>
          <div className="flex items-center justify-center h-screen">
            <div className="flex flex-col items-center gap-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
              <p className="text-white text-lg">Verificando permissões...</p>
            </div>
          </div>
        </Sidebar>
      </>
    )
  }

  return (
    <>
      <Header />
      <Sidebar>
        <div className="m-3">
          <CoverForm title="Auditoria de contrato">
            <div className=" mb-5 flex items-center gap-4">
              <div>
                <p className="text-white mb-1">Tipo de conta</p>
                <select
                  value={typeAccount}
                  disabled={true}
                  onChange={({ target }) => setTypeAccount(target.value)}
                  className={`h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer`}
                >
                  <option value={"pf"} selected={true}>
                    Pessoa Física
                  </option>
                  <option value={"pj"}>Pessoa Jurídica</option>
                </select>
              </div>
              <div>
                <p className="text-white mb-1">Tipo de contrato</p>
                <select
                  value={contractType}
                  disabled={true}
                  onChange={({ target }) =>
                    setContractType(target.value === "mutuo" ? "mutuo" : "scp")
                  }
                  className={`h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer`}
                >
                  <option selected={true} value={"mutuo"}>
                    Mútuo
                  </option>
                  <option value={"scp"}>SCP</option>
                </select>
              </div>
            </div>
          </CoverForm>
          {typeAccount === "pj" ? (
            <BusinessContract
              contractData={contractPJ}
              modalityContract={contractType}
            />
          ) : (
            <PhysicalContract
              contractData={contractPF}
              modalityContract={contractType}
            />
          )}
        </div>
      </Sidebar>
    </>
  );
}
