'use client'

import Button from '@/components/Button/Button'
import {
  cepMask,
  clearLetters,
  cnpjMask,
  cpfMask,
  phoneMask,
  valueMask
} from '@/utils/masks'
import { useEffect, useState } from 'react'
import api from '@/core/api'
import returnError from '@/functions/returnError'
import { toast } from 'react-toastify'
import CoverForm from '@/components/CoverForm'
import moment from 'moment'
import { ContractPJ } from '../../types'
import InputSelectText from '@/components/Inputs/InputSelectText'
import Dropzone from '@/components/Dropzone'
import { FieldReason } from '@/components/Inputs/InputSelectText/types'
import { useNavigation } from '@/hooks/navigation'
import { useParams } from 'next/navigation'
import formatDate from '@/functions/formatDate'
import formatValue from '@/utils/formatValue'
import { getUserProfile } from '@/functions/getUserData'

interface IProps {
  modalityContract: 'mutuo' | 'scp'
  contractData?: ContractPJ
}

export default function BusinessContract({
  modalityContract,
  contractData
}: IProps) {
  const [loading, setLoading] = useState(false)
  const { id } = useParams()
  const [contract, setContract] = useState<FileList>()
  const [file, setFile] = useState<FileList>()
  const [document, setDocumet] = useState<FileList>()
  const [residence, setResidence] = useState<FileList>()
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
 

  const [auditContracts, setAuditContracts] = useState({
    contract: {
      auditAprove: true,
      reason: ''
    },
    proofPayment: {
      auditAprove: true,
      reason: ''
    },
    documentPdf: {
      auditAprove: true,
      reason: ''
    },
    proofOfResidence: {
      auditAprove: true,
      reason: ''
    }
  })

  const { navigation } = useNavigation()

  const [fieldReasons, setFieldReasons] = useState<FieldReason[]>([])

  const onSubmit = (type: 'APPROVED' | 'REJECTED') => {
    const contractReason = {
      field: 'contract',
      reason: auditContracts.contract.reason
    }
    const proofPaymentReason = {
      field: 'proofPayment',
      reason: auditContracts.proofPayment.reason
    }
    const documentReason = {
      field: 'documentPdf',
      reason: auditContracts.documentPdf.reason
    }
    const proofOfResidenceReason = {
      field: 'proofOfResidence',
      reason: auditContracts.proofOfResidence.reason
    }

    if (!auditContracts.contract.auditAprove) {
      fieldReasons.push(contractReason)
    } else {
      const d = fieldReasons.filter(field => field.field !== 'contract')
      setFieldReasons(d)
    }

    if (!auditContracts.proofPayment.auditAprove) {
      fieldReasons.push(proofPaymentReason)
    } else {
      const d = fieldReasons.filter(field => field.field !== 'proofPayment')
      setFieldReasons(d)
    }

    if (!auditContracts.documentPdf.auditAprove) {
      fieldReasons.push(documentReason)
    } else {
      const d = fieldReasons.filter(field => field.field !== 'documentPdf')
      setFieldReasons(d)
    }

    if (!auditContracts.proofOfResidence.auditAprove) {
      fieldReasons.push(proofOfResidenceReason)
    } else {
      const d = fieldReasons.filter(field => field.field !== 'proofOfResidence')
      setFieldReasons(d)
    }

    for (const field of fieldReasons) {
      if (field.reason === '') {
        toast.warn('O campo de motivo não pode estar vazio.')
        return
      }
    }

    fieldReasons.map(field => {
      if (field.reason === '') {
        return toast.warn('O campo de motivo não pode estar vazio.')
      }
    })

    if (type === 'APPROVED' && fieldReasons.length > 0) {
      return toast.warn(
        'Para aprovar o contrato não pode ter nenhum campo inválido marcado.'
      )
    }

    if (type === 'REJECTED' && fieldReasons.length === 0) {
      return toast.warn(
        'Para rejeitar um contrato selecione pelo menos um motivo.'
      )
    }

    
    if (type === 'APPROVED') setIsApproving(true);
    if (type === 'REJECTED') setIsRejecting(true);


    setLoading(true)
    api
      .post(`/audit/contract`, {
        contractId: id,
        decision: type,
        rejectionReasons: {
          reasons: fieldReasons
        }
      })
      .then(resp => {
        toast.success(
          `Contrato ${
            type === 'APPROVED' ? 'aprovado' : 'rejeitado'
          } com sucesso!`
        )
        navigation('/contratos')
      })
      .catch(err => {
        returnError(err, 'Falha ao auditar o contrato.')
      })
      .finally(() => {
        
    if (type === 'APPROVED') setIsApproving(false);
    if (type === 'REJECTED') setIsRejecting(false);

      })
  }

  const filterFieldReasons = (data: FieldReason, filter: string) => {
    if (data.field !== '') {
      setFieldReasons([...fieldReasons, data])
    } else {
      const filtered = fieldReasons.filter(field => field.field !== filter)
      setFieldReasons(filtered)
    }
  }

  const fieldReasonText = (data: { filter: string; text: string }) => {
    const exists = fieldReasons.some(field => field.field === data.filter)

    if (exists) {
      const updated = fieldReasons.map(field =>
        field.field === data.filter ? { ...field, reason: data.text } : field
      )
      setFieldReasons(updated)
    } else {
      setFieldReasons([
        ...fieldReasons,
        {
          field: data.filter,
          reason: data.text
        }
      ])
    }
  }

  function durationInMonths() {
    const start = moment(contractData?.investment.start)
    const end = moment(contractData?.investment.end)

    return String(end.diff(start, 'months'))
  }

  return (
    <div>
      <CoverForm title="Dados do representante">
        <div className="flex items-start gap-4 flex-wrap ">
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.name}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="ownerName"
            width="300px"
            label="Nome completo"
          />
          <InputSelectText
            placeholder={cpfMask(
              contractData?.investor.responsibleOwner.document || ''
            )}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="ownerDocument"
            width="200px"
            label="CPF"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.rg}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="rg"
            width="200px"
            label="Identidade"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.issuingAgency}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="issuer"
            width="200px"
            label="Orgão emissor"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.nationality}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="placeOfBirth"
            width="200px"
            label="Nacionalidade"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.occupation}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="occupation"
            width="200px"
            label="Ocupação"
          />
          <InputSelectText
            placeholder={phoneMask(
              contractData?.investor.responsibleOwner.phone.replace(
                '+55',
                ''
              ) || ''
            )}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            width="200px"
            name="phoneNumber"
            label="Celular"
          />
          <InputSelectText
            placeholder={moment(
              contractData?.investor.responsibleOwner.birthDate
            ).format('DD/MM/YYYY')}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="dtBirth"
            width="200px"
            label="Data de Nascimento"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.email}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="email"
            width="300px"
            label="E-mail"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.motherName}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="motherName"
            width="300px"
            label="Nome da mãe"
          />
          <InputSelectText
            placeholder={
              contractData?.investor.responsibleOwner.address.zipcode
            }
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="zipCode"
            width="200px"
            label="CEP"
          />
          <InputSelectText
            placeholder={
              contractData?.investor.responsibleOwner.address.neighborhood
            }
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="neighborhood"
            width="200px"
            label="Bairro"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.address.street}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="street"
            width="300px"
            label="Rua"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.address.city}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="city"
            width="200px"
            label="Cidade"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.address.state}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="state"
            width="150px"
            label="Estado"
          />
          <InputSelectText
            placeholder={contractData?.investor.responsibleOwner.address.number}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="number"
            width="200px"
            label="Número"
          />
          <InputSelectText
            placeholder={
              contractData?.investor.responsibleOwner.address.complement
            }
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="complement"
            width="200px"
            label="Complemento"
          />
        </div>
      </CoverForm>
      <CoverForm title="Dados da empresa">
        <div className="flex items-start gap-4 flex-wrap ">
          <InputSelectText
            placeholder={contractData?.investor.companyName}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="companyName"
            width="300px"
            label="Nome"
          />
          <InputSelectText
            placeholder={cnpjMask(contractData?.investor.document || '')}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="document"
            width="200px"
            label="CNPJ"
          />
          <InputSelectText
            placeholder={contractData?.investor.fantasyName}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="fantasyName"
            width="300px"
            label="Nome fantasia"
          />
          <InputSelectText
            placeholder={contractData?.investor.businessType}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="businessType"
            width="150px"
            label="Tipo"
          />
          <InputSelectText
            placeholder={formatDate(contractData?.investor.openingDate || '')}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="openingDate"
            width="180px"
            label="Data de abertura"
          />
          <InputSelectText
            placeholder={contractData?.investor.email}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="email"
            width="300px"
            label="E-mail"
          />
          <InputSelectText
            placeholder={contractData?.investor.address.zipcode}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="zipCode"
            width="200px"
            label="CEP"
          />
          <InputSelectText
            placeholder={contractData?.investor.address.neighborhood}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="neighborhood"
            width="200px"
            label="Bairro"
          />
          <InputSelectText
            placeholder={contractData?.investor.address.street}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="street"
            width="300px"
            label="Rua"
          />
          <InputSelectText
            placeholder={contractData?.investor.address.city}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="city"
            width="200px"
            label="Cidade"
          />
          <InputSelectText
            placeholder={contractData?.investor.address.state}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="state"
            width="150px"
            label="Estado"
          />
          <InputSelectText
            placeholder={contractData?.investor.address.number}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="number"
            width="200px"
            label="Número"
          />
          <InputSelectText
            placeholder={contractData?.investor.address.complement}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="complement"
            width="200px"
            label="Complemento"
          />
        </div>
      </CoverForm>
      <CoverForm title="Dados de Investimento">
        <div className="flex items-start gap-4 flex-wrap">
          <InputSelectText
            placeholder={`${formatValue(
              Number(contractData?.investment.value)
            )}`}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="value"
            width="200px"
            label="Valor"
          />
          <InputSelectText
            placeholder={durationInMonths()}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="term"
            width="250px"
            label="Prazo investimento - em meses"
          />
          <InputSelectText
            placeholder={`${contractData?.investment.yield || 0}%`}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="yield"
            width="250px"
            label="Taxa Remuneração Mensal - em %"
          />
          <InputSelectText
            placeholder={contractData?.investment.purchasedWith}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="purchasedWith"
            width="250px"
            label="Comprar com"
          />
          {modalityContract === 'scp' && (
            <>
              <InputSelectText
                placeholder={contractData?.investment.quotesAmount}
                setFieldText={fieldReasonText}
                fieldsReasons={fieldReasons}
                setFieldReason={filterFieldReasons}
                name="amountQuotes"
                width="150px"
                label="Quantidade de cotas"
              />
            </>
          )}
          <InputSelectText
            placeholder={moment(contractData?.investment.start).format(
              'DD/MM/YYYY'
            )}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="initDate"
            width="200px"
            label="Inicio do contrato"
          />
          <InputSelectText
            placeholder={moment(contractData?.investment.end).format(
              'DD/MM/YYYY'
            )}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="endDate"
            width="200px"
            label="Final do contrato"
          />
          <InputSelectText
            placeholder={contractData?.investment.isdebenture ? 'Sim' : 'Não'}
            setFieldText={fieldReasonText}
            fieldsReasons={fieldReasons}
            setFieldReason={filterFieldReasons}
            name="isDebenture"
            width="200px"
            label="É Debênture?"
          />
        </div>
      </CoverForm>
      <CoverForm title="Anexo de documentos">
        <div>
          <div className="flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white">
            <div>
              <p
                className={`mb-1 ${
                  auditContracts.contract.auditAprove ? '' : 'text-red-600'
                }`}
              >
                Contrato
              </p>
              <div
                onClick={() =>
                  setAuditContracts({
                    ...auditContracts,
                    contract: {
                      ...auditContracts.contract,
                      auditAprove: !auditContracts.contract.auditAprove
                    }
                  })
                }
                className={`${
                  auditContracts.contract.auditAprove
                    ? ''
                    : 'border-red-600 border-[3px]'
                }`}
              >
                <Dropzone disable={true} onFileUploaded={setContract} />
              </div>
              {!auditContracts.contract.auditAprove && (
                <div>
                  <input
                    onChange={({ target }) => {
                      setAuditContracts({
                        ...auditContracts,
                        contract: {
                          ...auditContracts.contract,
                          reason: target.value
                        }
                      })
                    }}
                    placeholder="Motivo"
                    className="h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"
                  />
                </div>
              )}
              {contractData?.files.contractPdf ? (
                <div>
                  <Button
                    label={`Ver documento`}
                    className={`mt-2 bg-orange-linear`}
                    loading={false}
                    size="sm"
                    handleSubmit={() =>
                      window.open(contractData?.files.contractPdf, '_blanck')
                    }
                  />
                </div>
              ) : (
                <p className="text-sm">Documento não anexado</p>
              )}
            </div>
            <div>
              <p
                className={`mb-1 ${
                  auditContracts.proofPayment.auditAprove ? '' : 'text-red-600'
                }`}
              >
                Comprovante
              </p>
              <div
                onClick={() =>
                  setAuditContracts({
                    ...auditContracts,
                    proofPayment: {
                      ...auditContracts.proofPayment,
                      auditAprove: !auditContracts.proofPayment.auditAprove
                    }
                  })
                }
                className={`${
                  auditContracts.proofPayment.auditAprove
                    ? ''
                    : 'border-red-600 border-[3px]'
                }`}
              >
                <Dropzone disable={true} onFileUploaded={setFile} />
              </div>
              {!auditContracts.proofPayment.auditAprove && (
                <div>
                  <input
                    onChange={({ target }) => {
                      setAuditContracts({
                        ...auditContracts,
                        proofPayment: {
                          ...auditContracts.proofPayment,
                          reason: target.value
                        }
                      })
                    }}
                    placeholder="Motivo"
                    className="h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"
                  />
                </div>
              )}
              {contractData?.files.proofPaymentPdf ? (
                <div>
                  <Button
                    label={`Ver documento`}
                    className={`mt-2 bg-orange-linear`}
                    loading={false}
                    size="sm"
                    handleSubmit={() =>
                      window.open(
                        contractData?.files.proofPaymentPdf,
                        '_blanck'
                      )
                    }
                  />
                </div>
              ) : (
                <p className="text-sm">Documento não anexado</p>
              )}
            </div>
            <div>
              <p
                className={`mb-1 ${
                  auditContracts.documentPdf.auditAprove ? '' : 'text-red-600'
                }`}
              >
                Documento de identidade
              </p>
              <div
                onClick={() =>
                  setAuditContracts({
                    ...auditContracts,
                    documentPdf: {
                      ...auditContracts.documentPdf,
                      auditAprove: !auditContracts.documentPdf.auditAprove
                    }
                  })
                }
                className={`${
                  auditContracts.documentPdf.auditAprove
                    ? ''
                    : 'border-red-600 border-[3px]'
                }`}
              >
                <Dropzone disable={true} onFileUploaded={setDocumet} />
              </div>
              {!auditContracts.documentPdf.auditAprove && (
                <div>
                  <input
                    onChange={({ target }) => {
                      setAuditContracts({
                        ...auditContracts,
                        documentPdf: {
                          ...auditContracts.documentPdf,
                          reason: target.value
                        }
                      })
                    }}
                    placeholder="Motivo"
                    className="h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"
                  />
                </div>
              )}
              {contractData?.files.personalDocument ? (
                <div>
                  <Button
                    label={`Ver documento`}
                    className={`mt-2 bg-orange-linear`}
                    loading={false}
                    size="sm"
                    handleSubmit={() =>
                      window.open(
                        contractData?.files.personalDocument,
                        '_blanck'
                      )
                    }
                  />
                </div>
              ) : (
                <p className="text-sm">Documento não anexado</p>
              )}
            </div>
            <div>
              <p
                className={`mb-1 ${
                  auditContracts.proofOfResidence.auditAprove
                    ? ''
                    : 'text-red-600'
                }`}
              >
                Comprovante de residência
              </p>
              <div
                onClick={() =>
                  setAuditContracts({
                    ...auditContracts,
                    proofOfResidence: {
                      ...auditContracts.proofOfResidence,
                      auditAprove: !auditContracts.proofOfResidence.auditAprove
                    }
                  })
                }
                className={`${
                  auditContracts.proofOfResidence.auditAprove
                    ? ''
                    : 'border-red-600 border-[3px]'
                }`}
              >
                <Dropzone disable={true} onFileUploaded={setDocumet} />
              </div>
              {!auditContracts.proofOfResidence.auditAprove && (
                <div>
                  <input
                    onChange={({ target }) => {
                      setAuditContracts({
                        ...auditContracts,
                        proofOfResidence: {
                          ...auditContracts.proofOfResidence,
                          reason: target.value
                        }
                      })
                    }}
                    placeholder="Motivo"
                    className="h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"
                  />
                </div>
              )}
              {contractData?.files.proofOfResidence ? (
                <div>
                  <Button
                    label={`Ver documento`}
                    className={`mt-2 bg-orange-linear`}
                    loading={false}
                    size="sm"
                    handleSubmit={() =>
                      window.open(
                        contractData?.files.proofOfResidence,
                        '_blanck'
                      )
                    }
                  />
                </div>
              ) : (
                <p className="text-sm">Documento não anexado</p>
              )}
            </div>
          </div>
        </div>
      </CoverForm>
      <div className="flex gap-4 w-full justify-end">
        <div className="md:w-52 mb-10">
          <Button
            label="Rejeitar"
            loading={isRejecting}
            disabled={loading}
            handleSubmit={() => onSubmit('REJECTED')}
            className="bg-red-700 hover:bg-red-800"
          />
        </div>
        <div className="md:w-52 mb-10">
          <Button
            label="Aprovar"
            loading={isApproving}
            disabled={loading}
            handleSubmit={() => onSubmit('APPROVED')}
          />
        </div>
      </div>
    </div>
  )
}
