import { AuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";


export const authConfig: AuthOptions = {
    providers: [
    CredentialsProvider({ 
        name: 'credentials',
        credentials: {
            email: {},
            password: {},
        }
    },
   async authorize(credentials) {
    const res = await fetch('',{
        
    })
   }

)
    ]
}