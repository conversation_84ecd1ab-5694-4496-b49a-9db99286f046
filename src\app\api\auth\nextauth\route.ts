import { AuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";


export const authConfig: AuthOptions = {
    providers: [
        CredentialsProvider({
            name: "credentials",
            credentials: {
                email: {},
                password: {}
            },
            async authorize(credentials) {
                const res = await fetch('/auth-backoffice/login', {
                    method: "POST",
                    headers: { "Content-Type": "application/json"},
                    body: JSON.stringify(credentials)
                });

                const user = await res.json();
                if(res.ok && user.token) {
                    return {...user, id: user.id};
                }
                return null;
            }
        })
    ],
    session: {
        strategy: "jwt"
    },
    callbacks: {
        async jwt({token, user}) {
            if(user){
                token.accessToken = user.token;
                token.role = user.roles;
            }
            return token;
        },
        async session({session, token}) {
            session.accessToken = token.accessToken;
            session.role = token.role;
            return session;
        },

    },
    secret: process.env.NEXTAUTH_SECRET,
}