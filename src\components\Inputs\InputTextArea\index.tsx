import { useEffect } from "react"
import { UseFormRegister } from "react-hook-form"

interface IProps {
  setValue?: (d: any) => void
  error?: boolean
  errorMessage?: string
  width?: string
  register: UseFormRegister<any>
  name: string
  placeholder?: string
}

export default function InputTextArea({
  setValue,
  error,
  errorMessage,
  width='100%',
  register,
  name,
  placeholder='',
}: IProps) {
  useEffect(() => {

  }, [width])

  return (
    <div className="input" style={{width}}>
      <textarea
        {...register(name)}
        placeholder={placeholder}
        onChange={({target}) => {
          if(setValue) {
            setValue(target.value)
          }
        }}
        className={`h-12 w-full min-h-40 p-4 text-white rounded-xl ${error ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
      />
    </div>
  )
}