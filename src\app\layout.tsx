"use client";

import AuthProvider from "@/provider/AuthProvider";
import { QueryClient } from "@tanstack/react-query";
import "./globals.css";
import "react-toastify/dist/ReactToastify.css";
import { Poppins } from "next/font/google";
import { ToastContainer } from "react-toastify";
import { checkIsPublicRoute } from "@/functions/checkPrivateRoutes";
import { usePathname } from "next/navigation";
import PrivateRoute from "@/components/PrivateRoute";
import { ReactQueryClientProvider } from "@/provider/ReactQueryClientProvider";

const poppins = Poppins({
  subsets: ["latin"], // Suporte para caracteres latinos
  weight: ["400", "500", "600"], // Escolha os pesos desejados
  variable: "--font-poppins", // Nome da variável CSS
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathName = usePathname();

  const isPublicPage = checkIsPublicRoute(`/${pathName.split("/")[1]}`);

  return (
    <html lang="pt-br">
      <head>
        <title>Ica Invest Contracts</title>
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={poppins.variable}>
        <ReactQueryClientProvider>
          <ToastContainer theme="light" />
          <AuthProvider>
            {isPublicPage && children}
            {!isPublicPage && <PrivateRoute>{children}</PrivateRoute>}
          </AuthProvider>
        </ReactQueryClientProvider>
      </body>
    </html>
  );
}
