import { ContractStatus } from "@/functions/formatStatus"

const filterStatusContracts: {
  label: string;
  value: string;
}[] = [
  {
    label: "Todos",
    value: "Todos"
  },
  {
    label: "Contrato Gerado",
    value: ContractStatus.GENERATED
  },
  {
    label: "Envio da assinatura",
    value: ContractStatus.SIGNATURE_SENT
  },
  {
    label: "Aguardando assinatura do investidor",
    value: ContractStatus.AWAITING_INVESTOR_SIGNATURE
  },
  {
    label: "Aguardando comprovante de pagamento",
    value: ContractStatus.AWAITING_DEPOSIT
  },
  {
    label: "Aguardando auditoria",
    value: ContractStatus.AWAITING_AUDIT
  },
  {
    label: "Aguardando assinatura da auditoria",
    value: ContractStatus.AWAITING_AUDIT_SIGNATURE
  },
  {
    label: "Falha ao enviar contrato para assinatura",
    value: ContractStatus.SIGNATURE_FAILED
  },
  {
    label: "Expirado por falta de assinatura do investidor",
    value: ContractStatus.EXPIRED_BY_INVESTOR
  },
  {
    label: "Expirado por falta de assinatura da auditoria",
    value: ContractStatus.EXPIRED_BY_AUDIT
  },
  {
    label: "Rejeitado",
    value: ContractStatus.REJECTED
  },
  {
    label: 'Rejeitado pela auditoria',
    value: ContractStatus.REJECTED_BY_AUDIT,
  },
  {
    label: "Falha ao gerar o contrato",
    value: ContractStatus.GENERATE_CONTRACT_FAILED
  },
  {
    label: "Expirado",
    value: ContractStatus.EXPIRED
  },
]

export default filterStatusContracts