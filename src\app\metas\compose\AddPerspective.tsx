import Input from "@/components/Input";
import { Button } from "@/components/ui/button";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import { valueMask } from "@/utils/masks";
import { useState } from "react";
import { toast } from "react-toastify";

interface IProps {
  brokerGoalId: string
}

export default function AddPerspective({ brokerGoalId }: IProps) {
  const [value, setValue] = useState('')
  const [observation, setObservation] = useState('')
  const [loading, setLoading] = useState(false)
  const savePerspective = () => {
    setLoading(true)
    const payload = {
      brokerGoalId,
      observation,
      amount: Number(value.replaceAll('.', '').replace(',', '.')),
    }

    api.patch('/goals/perspective-broker-amount', payload).then(resp => {
      toast.success("Perspectiva cadastrada com sucesso!")
      window.location.reload()
    }).catch(err => {
      returnError(err, 'Erro ao criar perspectiva!')
    }).finally(() => setLoading(false))
  }

  return (
    <div className="mt-10">
      <div>
        <p className="text-white mb-2">Adicionar o valor</p>
        <div className="w-72">
          <Input bg="fill" id="" label="" placeholder="R$" name="" type="text" value={value} onChange={(e) => {
            setValue(valueMask(e.target.value))
          }} />
        </div>
      </div>
      <div className="mt-5">
        <p className="text-white mb-2">Adicione Observações (caso necessário)</p>
        <div className="w-[500px]">
          <textarea
            value={observation}
            placeholder="Observações"
            className={`bg-[#1C1C1C] h-32 w-full p-2 text-white rounded-xl ring-1 ring-inset flex-1 ring-[#FF9900]`}
            onChange={({target}) => setObservation(target.value)}
          />
        </div>
      </div>
      <div>
      <div className="flex justify-end mt-10">
        <div>
          {/* <p className="bg-orange-linear px-5 py-1 rounded-xl text-sm cursor-pointer select-none" onClick={savePerspective}>Concluir</p> */}
          <Button onClick={savePerspective} loading={loading}>
            Concluir
          </Button>
        </div>
        
      </div>
      </div>
    </div>
  )
}