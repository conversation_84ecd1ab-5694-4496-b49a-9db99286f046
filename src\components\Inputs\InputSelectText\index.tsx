import { useEffect, useState } from "react"
import { UseFormRegister } from "react-hook-form"

import './style.css'
import { FieldReason, IProps } from "./types"

export default function InputSelectText({
  label,
  width='auto',
  name,
  placeholder='',
  fieldsReasons,
  setFieldReason,
  setFieldText
}: IProps) {
  const field = fieldsReasons?.find(field => field.field === name)
  const [text, setText] = useState('')
  useEffect(() => {
    if (field?.reason !== undefined) {
      setText(field.reason)
    }
  }, [field?.reason])


  return (
    <div className="input" style={{width}}>
      <p className="text-white mb-1 text-sm">{label}</p>
      <div>
        <p
          onClick={() => {
            if(setFieldReason) {
              if(field?.field) {
                setFieldReason({
                  field: "",
                  reason: ""
                }, name)
              } else {
                setFieldReason({
                  field: name,
                  reason: ""
                }, name)
              }
            }
          }}
          className={`py-3 min-h-[48px] w-full select-none cursor-pointer px-4 text-white rounded-xl ${field?.field ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
        >{placeholder}</p>
        {
          field?.field && (
            <input 
              type="text"
              onChange={({ target }) => {
                setText(target.value)
              }} 
              onBlur={() => {
                if (setFieldText) {
                  setFieldText({
                    filter: name,
                    text: text
                  })
                }
              }}
              value={text}
              placeholder="Motivo"
              className="h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"
            />
          )
        }
      </div>
    </div>
  )
}