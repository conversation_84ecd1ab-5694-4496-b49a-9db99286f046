import React, { useState } from "react";
import { SkeletonItem } from "../Skeleton/intex";
import Pagination from "../Pagination";
import { TableData, TableProps } from "./types";
import { CheckIcon } from "@heroicons/react/24/outline";

const Table = <T extends TableData>({ data, headers, loading, pagination, selectable = false, checked = false, setChecked = () => {}}: TableProps<T>) => {
  return (
    <div className='bg-[#1C1C1C] w-full text-white overflow-x-auto min-h-[350px] flex flex-col'>
      <div className="flex-1 mb-1">
        <table className='w-full '>
          <thead className='w-full bg-[#313131] border-y border-y-[#FF9900] relative'>
          {
              selectable && (
                <div className="absolute p-1 pl-4 z-10 select-none">
                  <div className="w-5 h-5 border rounded-md border-[#FF9900] cursor-pointer flex items-center justify-center" onClick={() => {
                      setChecked(!checked)
                  }}>
                    {checked && <CheckIcon width={20} color="#1EF97C" />}
                  </div>
                </div>
              )
            }
            <tr className='w-full'>
              {headers.map((header, index) => (
                <th key={index} style={{ width: header.width }} className='py-2 pl-4'>
                  <p className={`font-bold text-sm ${header.position && `text-${header.position}`}`}>{header.title}</p>
                </th>
              ))}
            </tr>
          </thead>
          <>
            <tbody className='w-full'>
              {
                !loading ? data.length > 0 ? (
                  data.map((row, rowIndex) => (
                    <tr key={rowIndex} className="">
                    {headers.map((header, index) => {

                      return (
                        <td key={header.component} style={{ width: header.width }} className={`text-sm py-2 pl-4`}>
                          {
                            header?.render ? header.render(row[header.component], row) : <p style={{ width: header.width }} className={`${header.position && `text-${header.position}`} truncate whitespace-nowrap overflow-hidden`}>{row[header.component]}</p>
                          }
                        </td>
                      )
                    })}
                  </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={headers.length} className="py-2 px-4 text-center pt-10">
                      Nenhum dado encontrado
                    </td>
                  </tr>
                ) : (
                  <tr className="">
                    {headers.map((header) => (
                      <td key={header.component} className="p-1">
                        <SkeletonItem height='25px' />
                      </td>
                    ))}
                  </tr>
                )
              }
            </tbody>
          </>
        </table>
      </div>
      {
        pagination && (
          <Pagination 
            lastPage={pagination.lastPage} 
            page={pagination.page} 
            perPage={pagination.perPage} 
            setPage={pagination.setPage} 
            totalItems={pagination.totalItems} 
            loading={loading} // Passamos o estado de loading para o Pagination
          />
        )
      }
    </div>
  );
};

export default Table;
