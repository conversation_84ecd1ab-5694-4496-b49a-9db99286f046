import Button from "@/components/Button/Button"
import Dropzone from "@/components/Dropzone"
import api from "@/core/api"
import User, { UserProfile } from "@/models/user"
import { cepMask, clearLetters, cnpjMask, cpfMask, phoneMask, valueMask } from "@/utils/masks"
import axios from "axios"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "react-toastify"
import { yupResolver } from '@hookform/resolvers/yup'
import { schemaRegisterManualBroker } from "@/utils/schemas/schemasValidation"
import Input from "@/components/Input"
import SelectSearch from "@/components/SelectSearch"
import returnError from "@/functions/returnError"
import { isValidateCNPJ, isValidateCPF } from "@/utils/validate-documents"
import { getUserProfile } from "@/functions/getUserData"
import validatePhoneNumber from "@/utils/validatePhoneNumber"
import isValidUF from "@/utils/isValidUf"
import moment from "moment"
import SelectCustom from '@/components/SelectCustom';

interface IProps {
  typeCreate: 'broker' | 'advisor' | 'admin'
  hide?: boolean
}

interface BrokerAcessor {
  name: string
  id: string
}

export default function CreateBroker({ typeCreate, hide }: IProps) {
  const [loading, setLoading] = useState(false)
  const [password, setPassword] = useState('')
  const [brokers, setBrokers] = useState<BrokerAcessor[]>([])
  const [admins, setAdmins] = useState<BrokerAcessor[]>([])
  const [document, setDocumet] = useState<FileList>()
  const [card, setCard] = useState<FileList>()
  const [mei, setMei] = useState<FileList>()
  const [residence, setResidence] = useState<FileList>()
  const [social, setSocial] = useState<FileList>()
  const [parcer, setParcer] = useState<FileList>()
  const [accountCreated, setAccountCreated] = useState(false)
  const [accountCreatedId, setAccountCreatedId] = useState('')

  const [documensSended, setDocumentsSended] = useState({
    document: false,
    card: false,
    mei: false,
    residence: false,
    social: false,
    parcer: false
  })

  const [typeAccount, setTypeAccount] = useState('')
  const [brokerid, setBrokerId] = useState('')
  const [adminId, setAdminId] = useState('')
  const userProfile = getUserProfile()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schemaRegisterManualBroker),
    mode: "onChange",
    context: { hide }
  })

  const ownerCep = watch('ownerCep')
  const businessCep = watch('businessCep')
  const typeBusiness = watch('type')

  const returnText = () => {
    switch (typeCreate) {
      case "broker": return 'broker'
      case "advisor": return 'assessor'
      case "admin": return 'admin / gestor de carteiras'
    }
  }

  useEffect(() => {
    if (typeAccount === 'pf') {
      setValue('isPf', true)
    } else {
      setValue('isPf', false)
    }
  }, [typeAccount])

  function handleSendDocument(type: any) {
    setDocumentsSended(prevState => ({
      ...prevState,
      [type]: true,
    }))
  }

  const sendDocuments = (id: string) => {
    const mapDocumentsToSend = [
      {
        status: documensSended.document,
        document: document,
        type: 'RG',
        title: 'Documento de identidade',
        active: true,
        doc: 'document'
      },
      {
        status: documensSended.residence,
        document: residence,
        type: 'PROOF_RESIDENCE',
        title: 'Comprovante de residência',
        active: true,
        doc: 'residence'
      },
      {
        status: documensSended.parcer,
        document: parcer,
        type: 'CONTRACT',
        title: 'Contrato de parceria',
        active: true,
        doc: 'parcer'
      },

    ]

    if (typeAccount === 'pj') {
      mapDocumentsToSend.push({
        status: documensSended.social,
        document: social,
        type: 'SOCIAL_CONTRACT',
        title: 'Contrato social',
        active: true,
        doc: 'social'
      })
      mapDocumentsToSend.push({
        status: documensSended.card,
        document: card,
        type: 'CARD_CNPJ',
        title: 'Cartão cnpj',
        active: true,
        doc: 'card'
      },)
    }

    if (typeAccount === 'pj' && typeBusiness === 'MEI') {
      mapDocumentsToSend.push({
        status: documensSended.mei,
        document: mei,
        type: 'MEI',
        title: 'Certificado de mei',
        active: true,
        doc: 'mei'
      })
    }


    mapDocumentsToSend.map((doc, index) => {
      if (doc.document) {
        toast.info('Enviando documentos anexados...', {
          autoClose: false,
          toastId: 'sendDocuments'
        })
        if (!doc.status && doc.active === true) {
          const form = new FormData()
          form.append('id', id)
          form.append('type', doc.type)
          if (doc.document) {
            form.append('file', doc.document[0])
          }
          api.post('/uploads/documents', form).then(resp => {
            toast.success(`${doc.title} enviado com sucesso!`)
            handleSendDocument(doc.doc)
            if (mapDocumentsToSend.length === index + 1) {
              setAccountCreated(false)
              setDocumentsSended({
                document: false,
                card: false,
                mei: false,
                residence: false,
                social: false,
                parcer: false
              })
              setParcer(undefined)
              setSocial(undefined)
              setResidence(undefined)
              setMei(undefined)
              setCard(undefined)
              setDocumet(undefined)
              toast.success(`${returnText()} cadastrado com sucesso!`)
              toast.dismiss('sendDocuments')
            }
          }).catch(err => {
            returnError(err, `Erro ao enviar o ${doc.title}.`)
          })
        }
      }
    })
  }

  const onSubmit = (data: any) => {

    if (accountCreated) {
      return sendDocuments(accountCreatedId)
    }
    const type = typeAccount === 'pf' ? 'PHYSICAL' : 'BUSINESS'

    if (typeCreate === 'broker' && userProfile.name === 'superadmin' && !adminId) {
      return toast.warn("Selecione o gestor de carteira para vincular o broker.")
    }

    if (typeCreate === 'advisor' && userProfile.name !== "broker" && !brokerid) {
      return toast.warn("Selecione o broker para vincular o assessor.")
    }

    if (type === 'PHYSICAL') {
      if (!isValidateCPF(clearLetters(String(data.cpf || '')))) {
        return toast.warn("CPF do investidor inválido!")
      }
      if (!validatePhoneNumber(data.phoneNumber)) {
        return toast.warn("Número de telefone inválido")
      }

      if (!isValidUF(data.ownerState)) {
        return toast.warn("Estado inválido")
      }

    } else {
      if (!isValidateCNPJ(clearLetters(String(data.cnpj || '')))) {
        return toast.warn("CNPJ inválido!")
      }

      if (!isValidateCPF(clearLetters(String(data.cpf || '')))) {
        return toast.warn("CPF do representante inválido!")
      }

      if (!validatePhoneNumber(data.businessPhoneNumber)) {
        return toast.warn("Número de telefone inválido")
      }

      if (!isValidUF(data.ownerState)) {
        return toast.warn("Estado do representante inválido")
      }

      if (!isValidUF(data.ownerState)) {
        return toast.warn("Estado da empresa inválido")
      }
    }

    setLoading(true)
    const owner = {
      birthDate: data.birthDate,
      socialName: data.fullName,
      isTaxable: data.isTaxable === 's' ? true : false,
      fullName: data.fullName,
      cpf: clearLetters(String(data.cpf || '')),
      email: data.email,
      phoneNumber: `55${clearLetters(data.phoneNumber || '')}`,
      motherName: data.motherName,
      pep: data.pep === 's' ? true : false,
      password: password !== '' ? password : undefined,
      address: {
        cep: clearLetters(data.ownerCep || ""),
        city: data.ownerCity,
        state: data.ownerState,
        neighborhood: data.ownerNeighborhood,
        street: data.ownerStreet,
        complement: data.ownerComplement,
        number: data.ownerNumber,
      }
    }
    const business = {
      fantasyName: data.fantasyName,
      cnpj: clearLetters(String(data.cnpj || '')),
      companyName: data.companyName,
      phoneNumber: `55${clearLetters(data.businessPhoneNumber || '')}`,
      isTaxable: data.isTaxable === 's' ? true : false,
      dtOpening: data.dtOpening,
      email: data.businessEmail,
      type: data.type,
      size: data.size,
      address: {
        cep: clearLetters(data.businessCep || ""),
        city: data.businessCity,
        state: data.businessState,
        neighborhood: data.businessNeighborhood,
        street: data.businessStreet,
        complement: data.businessComplement,
        number: data.businessNumber,
      }
    }

    const payload = {
      adminId: typeCreate === 'broker' ? userProfile.name === 'superadmin' ? adminId : userProfile.roleId : undefined,
      brokerId: typeCreate === 'advisor' ? userProfile.name === 'broker' ? userProfile.roleId : brokerid : undefined,
      ...(hide ? {} : { partPercent: data.participationPercentage }),
      create: {
        adminId: typeCreate === 'advisor' ? userProfile.roleId : undefined,
        accountType: type,
        owner,
        business: type === 'BUSINESS' ? business : undefined
      }
    }



    api.post(`/create-wallets/${typeCreate}`, payload).then(resp => {
      toast.success('Acesso cadastrado com sucesso!')
      setAccountCreated(true)
      setAccountCreatedId(resp.data.id)
      sendDocuments(resp.data.id)
      setPassword('')
      reset()
    }).catch(err => {
      returnError(err, `Erro ao cadastrar o ${returnText()}`)
    }).finally(() => setLoading(false))
  }

  async function handleGetByCep(ev: string, type: 'pf' | 'pj') {
    const cep = ev
    await axios.get(`https://viacep.com.br/ws/${cep}/json/`).then(response => {
      if (response && response.data) {
        if (!response.data.erro) {
          if (type === 'pf') {
            setValue('ownerCity', response.data.localidade)
            setValue('ownerState', response.data.uf)
            setValue('ownerNeighborhood', response.data.bairro)
            setValue('ownerStreet', response.data.logradouro)
          } else {
            setValue('businessCity', response.data.localidade)
            setValue('businessState', response.data.uf)
            setValue('businessNeighborhood', response.data.bairro)
            setValue('businessStreet', response.data.logradouro)
          }
        }
      }
    })
      .catch((error) => {
        returnError(error, 'Erro ao buscar o cep digitado')
      })
  }

  function getCep(value: string, type: 'pf' | 'pj') {
    const number = value;
    const cep = number?.replace(/[^0-9]/g, '');
    if (cep?.length === 8) {
      handleGetByCep(cep, type);
    } else {
    }
  }

  const handleRequiredDocs = () => {
    if (typeAccount === 'pj') {
      return {
        document: true,
        card: true,
        mei: false,
        residence: true,
        social: true,
        parcer: true
      }
    } else if (typeAccount === 'pj' && typeBusiness === 'MEI') {
      return {
        document: true,
        card: true,
        mei: true,
        residence: true,
        social: true,
        parcer: true
      }
    } else {
      return {
        document: true,
        card: false,
        mei: false,
        residence: true,
        social: false,
        parcer: true
      }
    }
  }

  useEffect(() => {
    if (JSON.stringify(handleRequiredDocs()) === JSON.stringify(documensSended)) {
      reset()
    }

  }, [documensSended])

  useEffect(() => {
    getCep(ownerCep, 'pf')
  }, [ownerCep])

  useEffect(() => {
    getCep(businessCep || '', 'pj')
  }, [businessCep])

  const [alreadyFetched, setAlreadyFetched] = useState(false)

  useEffect(() => {
    if (!userProfile.name || alreadyFetched) return;

    if (typeCreate === 'broker' && userProfile.name === 'superadmin') {
      getAdmins()
    }

    if (userProfile.name !== 'advisor' && userProfile.name !== 'broker') {
      getBrokers()
    }
  }, [typeCreate, userProfile.name])

  function getBrokers() {
    api.get(userProfile.name === 'superadmin' ? '/wallets/list-brokers' : '/wallets/admin/brokers').then(resp => {
      setBrokers(resp.data)
    }).catch(error => {
      returnError(error, 'Erro ao buscar a lista de brokers')
    }).finally(() => {
      setLoading(false)
      setAlreadyFetched(false)
    })
  }

  function getAdmins() {
    setLoading(true)
    api.get('/wallets/list-admin').then(resp => {
      const responseData: any[] = []
      resp.data.map((item: any) => {
        responseData.push({
          ...item,
          document: item?.document?.length <= 11 ? cpfMask(item?.document || '') : cnpjMask(item?.document || ''),
          type: 'admin'
        })
      })
      setAdmins(responseData)
    }).catch(error => {
      returnError(error, 'Erro ao buscar os brokers')
    }).finally(() => {
      setLoading(false)
      setAlreadyFetched(false)
    })
  }

  return (
    <div className='md:px-5'>
      <form action="" onSubmit={handleSubmit(onSubmit)} onKeyDown={(e) => {
        if (e.key === "Enter") {
          e.preventDefault(); // Bloqueia o envio ao pressionar Enter
        }
      }}>
        <div className='m-auto'>
          <p className='text-xl text-white mb-5'>Cadastro de {returnText()}</p>
          <div className="flex gap-4">
            <div className='md:w-1/4 mb-5'>
              <div className="">
                <p className="text-white mb-1">Tipo de conta</p>
                <SelectCustom 
                  value={typeAccount} 
                  onChange={({ target }) => setTypeAccount(target.value)}
                >
                  <option disabled selected value={''}>Selecione</option>
                  <option value={'pf'}>Pessoa Física</option>
                  <option value={'pj'}>Pessoa Juridica</option>
                </SelectCustom>
              </div>
            </div>
            {
              (typeCreate === 'advisor' && userProfile.name !== 'broker') && (
                <div className='md:w-3/4 mb-5'>
                  <SelectSearch label="Vincular ao Broker" items={brokers} value={brokerid} setValue={setBrokerId} />
                </div>
              )
            }
            {
              (typeCreate === 'broker' && userProfile.name === 'superadmin') && (
                <div className='md:w-3/4 mb-5'>
                  <SelectSearch label="Vincular ao gestor de carteiras" items={admins} value={adminId} setValue={setAdminId} />
                </div>
              )
            }
          </div>

          {
            typeAccount !== '' && (
              <div>
                <p className='text-lg text-white font-bold'>Dados Pessoais</p>
                <div className="mb-10 m-auto">
                  <div className='flex md:flex-row flex-col w-full gap-4 justify-between mt-2'>
                    <div className='md:w-2/4'>
                      <p className="text-white mb-1">Nome Completo <b className='text-red-500 font-light text-sm'>{errors.fullName && `- ${errors.fullName.message}`}</b></p>
                      <input
                        {...register('fullName')}
                        className={`h-12 w-full px-4 text-white rounded-xl ${errors.fullName ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                      />
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">CPF <b className='text-red-500 font-light text-sm'>{errors.cpf && `- ${errors.cpf.message}`}</b></p>
                        <input
                          {...register('cpf')}
                          onChange={({ target }) => {
                            const value = target.value
                            setValue('cpf', cpfMask(value))
                          }}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.cpf ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div className="">
                        <p className="text-white mb-1">Emitir Taxa<b className='text-red-500 font-light text-sm'>{errors.isTaxable && `- ${errors.isTaxable.message}`}</b></p>
                        <SelectCustom 
                          value={watch('isTaxable')} 
                          onChange={(e) => setValue('isTaxable', e.target.value)}
                        >
                          <option disabled selected>Selecione</option>
                          <option value={'s'}>Sim</option>
                          <option value={'n'}>Não</option>
                        </SelectCustom>
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Data de Nascimento <b className='text-red-500 font-light text-sm'>{errors.birthDate && `- ${errors.birthDate.message}`}</b></p>
                        <input
                          {...register('birthDate')}
                          type='date'
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.birthDate ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='flex md:flex-row flex-col w-full gap-4 justify-between mt-4'>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Telefone <b className='text-red-500 font-light text-sm'>{errors.phoneNumber && `- ${errors.phoneNumber.message}`}</b></p>
                        <input
                          {...register('phoneNumber')}
                          maxLength={15}
                          onChange={({ target }) => setValue('phoneNumber', String(phoneMask(target.value)))}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.phoneNumber ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">E-mail <b className='text-red-500 font-light text-sm'>{errors.email && `- ${errors.email.message}`}</b></p>
                        <input
                          {...register('email')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.email ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    {!hide && (
                      <div className="md:w-2/4">
                        <div>
                          <p className="text-white mb-1">
                            Taxa de participação em %
                            <b className="text-red-500 font-light text-sm">
                              {errors.participationPercentage &&
                                `- ${errors.participationPercentage.message}`}
                            </b>
                          </p>
                          <input
                            {...register("participationPercentage")}
                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.participationPercentage
                              ? "ring-[#f33636]"
                              : "ring-[#FF9900]"
                              } ring-1 ring-inset bg-black flex-1`}
                          />
                        </div>
                      </div>
                    )}
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Nome da mãe <b className='text-red-500 font-light text-sm'>{errors.motherName && `- ${errors.motherName.message}`}</b></p>
                        <input
                          {...register('motherName')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.motherName ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='flex md:flex-row flex-col w-full gap-4 justify-between mt-4'>
                    <div className='md:w-1/4'>
                      <div>
                        <Input
                          id=""
                          label="Senha"
                          type="password"
                          value={password}
                          setValue={setPassword}
                          name="password"
                          isPassword={true}
                        />
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Pessoa politicamente exposta? <b className='text-red-500 font-light text-sm'>{errors.pep && `- ${errors.pep.message}`}</b></p>
                        <SelectCustom 
                          value={watch('pep')} 
                          onChange={(e) => setValue('pep', e.target.value)}
                        >
                          <option disabled selected>Selecione</option>
                          <option value={'s'}>Sim</option>
                          <option value={'n'}>Não</option>
                        </SelectCustom>
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">CEP <b className='text-red-500 font-light text-sm'>{errors.ownerCep && `- ${errors.ownerCep.message}`}</b></p>
                        <input
                          {...register('ownerCep')}
                          onChange={({ target }) => setValue("ownerCep", cepMask(target.value))}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerCep ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Cidade <b className='text-red-500 font-light text-sm'>{errors.ownerCity && `- ${errors.ownerCity.message}`}</b></p>
                        <input
                          {...register('ownerCity')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerCity ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='flex md:flex-row flex-col w-full gap-4 justify-between mt-4'>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Estado <b className='text-red-500 font-light text-sm'>{errors.ownerState && `- ${errors.ownerState.message}`}</b></p>
                        <input
                          {...register('ownerState')}
                          maxLength={2}
                          onChange={({ target }) => setValue("ownerState", target.value.toUpperCase())}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerState ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Bairro <b className='text-red-500 font-light text-sm'>{errors.ownerNeighborhood && `- ${errors.ownerNeighborhood.message}`}</b></p>
                        <input
                          {...register('ownerNeighborhood')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerNeighborhood ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Número <b className='text-red-500 font-light text-sm'>{errors.ownerNumber && `- ${errors.ownerNumber.message}`}</b></p>
                        <input
                          {...register('ownerNumber')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerNumber ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='flex md:flex-row flex-col w-full gap-4 mt-4'>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Rua <b className='text-red-500 font-light text-sm'>{errors.ownerStreet && `- ${errors.ownerStreet.message}`}</b></p>
                        <input
                          {...register('ownerStreet')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerStreet ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Complemento <b className='text-red-500 font-light text-sm'>{errors.ownerComplement && `- ${errors.ownerComplement.message}`}</b></p>
                        <input
                          {...register('ownerComplement')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerComplement ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          }

          {
            typeAccount === 'pj' && (
              <div>
                <p className='text-lg text-white font-bold'>Dados da Empresa</p>
                <div className="mb-10 m-auto">
                  <div className='flex md:flex-row flex-col w-full gap-4 justify-between mt-2'>
                    <div className='md:w-2/4'>
                      <p className="text-white mb-1">Nome Fantasia <b className='text-red-500 font-light text-sm'>{errors.fantasyName && `- ${errors.fantasyName.message}`}</b></p>
                      <input
                        {...register('fantasyName')}
                        className={`h-12 w-full px-4 text-white rounded-xl ${errors.fantasyName ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                      />
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">CNPJ <b className='text-red-500 font-light text-sm'>{errors.cnpj && `- ${errors.cnpj.message}`}</b></p>
                        <input
                          {...register('cnpj')}
                          onChange={({ target }) => {
                            const value = target.value
                            setValue('cnpj', cnpjMask(value))
                          }}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.cnpj ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Data de Abertura <b className='text-red-500 font-light text-sm'>{errors.dtOpening && `- ${errors.dtOpening.message}`}</b></p>
                        <input
                          {...register('dtOpening')}
                          type='date'
                          max={moment.utc().format('YYYY-MM-DD')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.dtOpening ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='flex md:flex-row flex-col w-full gap-4 justify-between mt-4'>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Telefone <b className='text-red-500 font-light text-sm'>{errors.businessPhoneNumber && `- ${errors.businessPhoneNumber.message}`}</b></p>
                        <input
                          {...register('businessPhoneNumber')}
                          maxLength={15}
                          onChange={({ target }) => setValue('businessPhoneNumber', String(phoneMask(target.value)))}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessPhoneNumber ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">E-mail <b className='text-red-500 font-light text-sm'>{errors.businessEmail && `- ${errors.businessEmail.message}`}</b></p>
                        <input
                          {...register('businessEmail')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessEmail ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Nome da Companhia <b className='text-red-500 font-light text-sm'>{errors.companyName && `- ${errors.companyName.message}`}</b></p>
                        <input
                          {...register('companyName')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.companyName ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='flex md:flex-row flex-col w-full gap-4 mt-4'>
                    <div className='md:w-1/4'>
                      <p className="text-white mb-1">Tipo<b className='text-red-500 font-light text-sm'>{errors.type && `- ${errors.type.message}`}</b></p>
                      <SelectCustom 
                        value={watch('type')} 
                        onChange={(e) => setValue('type', e.target.value)}
                      >
                        <option selected value={'MEI'}>MEI</option>
                        <option value={'EI'}>EI</option>
                        <option value={'EIRELI'}>EIRELI</option>
                        <option value={'SLU'}>SLU</option>
                        <option value={'LTDA'}>LTDA</option>
                        <option value={'SA'}>SA</option>
                        <option value={'TS'}>TS</option>
                      </SelectCustom>
                    </div>
                    <div className='md:w-1/4'>
                      <p className="text-white mb-1">Tamanho<b className='text-red-500 font-light text-sm'>{errors.size && `- ${errors.size.message}`}</b></p>
                      <SelectCustom 
                        value={watch('size')} 
                        onChange={(e) => setValue('size', e.target.value)}
                      >
                        <option selected value={'MEI'}>MEI</option>
                        <option value={'ME'}>ME</option>
                        <option value={'EPP'}>EPP</option>
                        <option value={'SMALL'}>SMALL</option>
                        <option value={'MEDIUM'}>MEDIUM</option>
                        <option value={'LARGE'}>LARGE</option>
                      </SelectCustom>
                    </div>
                  </div>
                  <div className='flex md:flex-row flex-col w-full gap-4 justify-between mt-4'>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">CEP <b className='text-red-500 font-light text-sm'>{errors.businessCep && `- ${errors.businessCep.message}`}</b></p>
                        <input
                          {...register('businessCep')}
                          onChange={({ target }) => setValue("businessCep", cepMask(target.value))}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessCep ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Estado <b className='text-red-500 font-light text-sm'>{errors.businessState && `- ${errors.businessState.message}`}</b></p>
                        <input
                          {...register('businessState')}
                          maxLength={2}
                          onChange={({ target }) => setValue("businessState", target.value.toUpperCase())}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessState ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Cidade <b className='text-red-500 font-light text-sm'>{errors.businessCity && `- ${errors.businessCity.message}`}</b></p>
                        <input
                          {...register('businessCity')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessCity ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Bairro <b className='text-red-500 font-light text-sm'>{errors.businessNeighborhood && `- ${errors.businessNeighborhood.message}`}</b></p>
                        <input
                          {...register('businessNeighborhood')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessNeighborhood ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>

                  <div className='flex md:flex-row flex-col w-full gap-4 mt-4'>
                    <div className='md:w-1/4'>
                      <div>
                        <p className="text-white mb-1">Número <b className='text-red-500 font-light text-sm'>{errors.businessNumber && `- ${errors.businessNumber.message}`}</b></p>
                        <input
                          {...register('businessNumber')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessNumber ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Rua <b className='text-red-500 font-light text-sm'>{errors.businessStreet && `- ${errors.businessStreet.message}`}</b></p>
                        <input
                          {...register('businessStreet')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessStreet ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                    <div className='md:w-2/4'>
                      <div>
                        <p className="text-white mb-1">Complemento <b className='text-red-500 font-light text-sm'>{errors.businessComplement && `- ${errors.businessComplement.message}`}</b></p>
                        <input
                          {...register('businessComplement')}
                          className={`h-12 w-full px-4 text-white rounded-xl ${errors.businessComplement ? 'ring-[#f33636]' : 'ring-[#FF9900]'} ring-1 ring-inset bg-black flex-1`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          }

          {
            !loading && (
              <div className='flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white'>
                <div>
                  <p className='mb-1'>Documento de identidade</p>
                  <Dropzone onFileUploaded={setDocumet} />
                </div>
                <div>
                  <p className='mb-1'>Comprovante de residência</p>
                  <Dropzone onFileUploaded={setResidence} />
                </div>
                {!hide && (
                  <div>
                    <p className="mb-1">Contrato de parceria</p>
                    <Dropzone onFileUploaded={setParcer} />
                  </div>
                )}
                {
                  typeAccount === 'pj' && (
                    <div>
                      <p className='mb-1'>Cartão cnpj</p>
                      <Dropzone onFileUploaded={setCard} />
                    </div>
                  )
                }
                {
                  typeAccount === 'pj' && (
                    <div>
                      <p className='mb-1'>Contrato social</p>
                      <Dropzone onFileUploaded={setSocial} />
                    </div>
                  )
                }
                {
                  typeBusiness === 'MEI' && typeAccount === 'pj' && (
                    <div>
                      <p className='mb-1'>Certificado de mei</p>
                      <Dropzone onFileUploaded={setMei} />
                    </div>
                  )
                }
              </div>
            )
          }
          <div className='md:w-52 mb-10'>
            <Button
              label='Enviar'
              loading={loading}
              disabled={loading}
            />
          </div>

        </div>
      </form>
    </div>
  )
}
