'use client'
import {useSession} from 'next-auth/react'
import { APP_ROUTES } from "@/constants/AppRoutes";
import api from "@/core/api";
import User, { UserProfile } from "@/models/user";
import { usePathname, useRouter } from "next/navigation";
import { ReactNode, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";

type PrivateRouteProps = {
  children: ReactNode;
};

const PrivateRoute = ({ children }: PrivateRouteProps) => {
  const { push } = useRouter();
  const [permision, setPermission] = useState<boolean>(true)
  const pathName = usePathname()
  const {data: session, status} = useSession()
  
  useEffect(() => {
    if (status === 'loading') return

    if (status === 'unauthenticated') {
      push(APP_ROUTES.public.login);
      return;
    }

    if (session) {
      if (session) {
        api.defaults.headers.common.authorizarion = `Bearer ${session.accessToken}`;
      }

      const userProfile = session.user?.activeProfile;
      const currentRoute = `/${pathName.split('/')[1]}`;

      if(!APP_ROUTES[userProfile?.name]?.includes(currentRoute)) {
        setPermission(false);
        toast.warning('Você não tem permissão para acessar essa página', {
          toastId: "not_permission_warn"
        });
        push('/home');
        return;
      }
    }

  }, []);

  
};

export default PrivateRoute;
