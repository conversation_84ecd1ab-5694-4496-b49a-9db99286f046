"use client";
import { useSession } from "next-auth/react";
import { APP_ROUTES } from "@/constants/AppRoutes";
import api from "@/core/api";
import User, { UserProfile } from "@/models/user";
import { usePathname, useRouter } from "next/navigation";
import { ReactNode, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";

type PrivateRouteProps = {
  children: ReactNode;
};

const PrivateRoute = ({ children }: PrivateRouteProps) => {
  const { push } = useRouter();
  const [permision, setPermission] = useState<boolean>(true);
  const pathName = usePathname();
  const { data: session, status } = useSession();

  useEffect(() => {
    // Se ainda está carregando, não fazer nada
    if (status === "loading") return;

    // Se não está autenticado, redirecionar para login
    if (status === "unauthenticated") {
      push(APP_ROUTES.public.login);
      return;
    }

    // Se está autenticado, configurar token e verificar permissões
    if (session) {
      // Configurar token no axios
      if (session.accessToken) {
        api.defaults.headers.common.Authorization = `Bearer ${session.accessToken}`;
      }

      // Verificar permissões
      const userProfile = session.user?.activeProfile;
      const currentRoute = `/${pathName.split("/")[1]}`;
      const validRoles = [
        "admin",
        "superadmin",
        "broker",
        "advisor",
        "investor",
        "retention",
        "financial",
      ];
      const roleName = userProfile?.name;

      if (roleName && validRoles.includes(roleName)) {
        const roleRoutes = APP_ROUTES[roleName as keyof typeof APP_ROUTES];
        if (Array.isArray(roleRoutes) && !roleRoutes.includes(currentRoute)) {
          setPermission(false);
          toast.warning("Você não tem permissão pra acessar essa página", {
            toastId: "not_permition_warn",
          });
          push("/home");
          return;
        } else {
          setPermission(true); // Importante: definir como true se tem permissão
        }
      }
    }
  }, [session, status, pathName, push, setPermission]);

  if (status === "loading") {
    return (
      <div className="flex min-h-screen item-center justify-center">
        <div>Carregando...</div>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return null;
  }

  if (!permision) {
    return null;
  }

  return <div>{children}</div>;
};

export default PrivateRoute;
