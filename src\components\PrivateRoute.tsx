'use client'

import { APP_ROUTES } from "@/constants/AppRoutes";
import api from "@/core/api";
import { getUserProfile } from "@/functions/getUserData";
import { restoreSessionData } from "@/functions/sessionStorageSecure";
import User, { UserProfile } from "@/models/user";
import AuthContext from "@/provider/AuthContext";
import { usePathname, useRouter } from "next/navigation";
import { ReactNode, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";

type PrivateRouteProps = {
  children: ReactNode;
};

const PrivateRoute = ({ children }: PrivateRouteProps) => {
  const { push } = useRouter();
  const [auth, setAuth] = useState<boolean>()
  const [token, setToken] = useState<string | null>()
  const [permision, setPermission] = useState<boolean>(true)
  const pathName = usePathname()
	const { notificationModal } = useContext(AuthContext)

  // Perform localStorage action
  
  useEffect(() => {
    // Restaura os dados da sessão
    restoreSessionData();
    
    // Verifica se houve troca de perfil
    const roleChanged = sessionStorage.getItem('role_changed') === 'true';
    if (roleChanged) {
      // Remove o flag após processar
      sessionStorage.removeItem('role_changed');
      // Força uma nova verificação de permissões com o perfil atualizado
      const userProfile = getUserProfile();
      
      // Verifica permissões com o novo perfil
      if (!APP_ROUTES[userProfile?.name]?.includes(`/${pathName.split('/')[1]}`)) {
        setPermission(false);
        toast.warning('Você não tem permissão para acessar essa página com o novo perfil', {
          toastId: "not_permission_warn"
        });
        push('/home');
        return;
      }
    }
    
    const userProfile = getUserProfile();
    
    var altenticated: boolean = sessionStorage.getItem("isAuthenticated") === "true" ? true : false;
    setAuth(altenticated);
    var token: string | null = sessionStorage.getItem("token");
    setToken(token);
    api.defaults.headers.common.Authorization = `Bearer ${token}`;
    
    if (!APP_ROUTES[userProfile?.name]?.includes(`/${pathName.split('/')[1]}`)) {
      setPermission(false);
      toast.warning('Você não tem permissão pra acessar essa página', {
        toastId: "not_permition_warn"
      });
    }

    if (!altenticated || !token || !permision) {
      push(APP_ROUTES.public.login);
      sessionStorage.clear();
    }
  }, [auth, push, token, pathName, permision]);

  return (
    <div className={`${notificationModal && 'fixed'}`}>
      {!auth && null}
      {auth && permision && children}
    </div>
  );
};

export default PrivateRoute;
