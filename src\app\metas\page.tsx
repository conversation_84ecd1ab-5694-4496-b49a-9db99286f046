"use client"
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import Image from "next/image";
import ProgressTable from "./compose/ProgresTable";
import { useEffect, useState } from "react";
import api from "@/core/api";
import { UserProfile } from "@/models/user";
import { toast } from "react-toastify";
import AddGoals from "./compose/AddGoals";
import AdminDashboard from "./compose/AdminDashboard";
import GoalDashboard from "./compose/GoalDashboard";
import moment from "moment";
import 'moment/locale/pt-br';
import InputSearch from "@/components/InputSearch";
import { AdjustmentsHorizontalIcon, ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import newFormatDate from "@/functions/newFormatDate";
import returnError from "@/functions/returnError";
import AddPerspective from "./compose/AddPerspective";
import Select from "@/components/Select";
import { getUserProfile } from "@/functions/getUserData";
import { Button } from "@/components/ui/button";

interface Goal {
  totalBrokerAchieved: number
  totalGoalAmount: number
  results: {
    goalName: string,
    brokerName: string,
    brokerDocument: string,
    targetAmount: number,
    totalAchieved: number,
    percentAchieved: number
    perspectiveAmount: number
  }[]
}

export interface IAdvisor {
  id: string,
  document: string,
  email: string,
  name: string
}

interface OptionSelect {
  label: string;
  value: string;
}

export default function Metas() {
  const [goals, setGoals] = useState<Goal>()
  const [goalBrokerId, setGoalBrokerId] = useState('')
  const [addGoal, setAddGoal] = useState<boolean>(false)
  const [addPerspective, setAddPerspective] = useState<boolean>(false)
  const [modalFilter, setModalFilter] = useState<boolean>(false)
  const [date, setDate] = useState('')
  const [advisors, setAdvisors] = useState<IAdvisor[]>()
  const [filterMetas, setFilterMetas] = useState<'goals' | 'perspective'>('goals');
  const [perspectiveSearch, setPerspectiveSeach] = useState<'goals' | 'perspective'>('goals');

  const [data, setData] = useState({
    percentage: 0,
    month: '',
    monthName: '',
    observation: '',
    over: 0,
    amountAchieved: 0,
    targetAmount: 0
  })

  function getDate() {
    let month = ''
    let year = ''
  
    if(date === '') {
      year = String(moment().year())
      month = String(moment().month() + 1)
    } else {
      year = date.split('-')[0]
      month = date.split('-')[1]
    }
  
    return {
      month,
      year
    }
  }

  const userProfile = getUserProfile()

  useEffect(() => {
    handleSelectTypeGoals()
  }, [addGoal])

  const options: OptionSelect[] = [
    { label: "Metas Internas", value: "goals" },
    { label: "Perspectiva dos Brokers", value: "perspective" },
  ];

  function handleSelectTypeGoals() {
    if (userProfile.name === 'broker') {
      getGoalsBroker()
      getAcessors()
    }

    if (userProfile.name === 'advisor') {
      getGoalsAdvisor()
    }

    if(userProfile.name === 'admin') {
      getGoalsadmin()
    }
    if(userProfile.name === 'superadmin') {
      getGoalsSuperAdmin()
      getBrokers()
    }
  }

  function getGoalsSuperAdmin() {
    api.get('/goals/superadmin/broker', {
      params: {
        year: getDate().year,
        month: getDate().month,
        type: filterMetas
      }
    }).then(resp => {
      const dateFrom = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-01`).dateFormated
      const dateTo = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-${moment(`${getDate().year}-${getDate().month}`, "YYYY-MM").endOf('month').date()}`).dateFormated
      setGoals(resp.data)
      const targetAmount = Number(resp.data.totalGoalAmount)
      const amountAchieved = Number(resp.data.totalBrokerAchieved)

      const percentage = (amountAchieved / targetAmount) * 100;
      setData({
        amountAchieved: resp.data.totalBrokerAchieved,
        month: `${dateFrom} à ${dateTo}`,
        monthName: newFormatDate(`${getDate().year}-${getDate().month}-01`).month,
        observation: '',
        over: Number(resp.data.totalGoalAmount) - Number(resp.data.totalBrokerAchieved),
        percentage: percentage,
        targetAmount: resp.data.totalGoalAmount
      })
    }).catch(err => {
      returnError(err, 'Não conseguimos encontrar as metas!')
    })
  }

  const getGoalsadmin = () => {
    api.get(`/goals/admin/${userProfile.roleId}/broker`, {
      params: {
        year: getDate().year,
        month: getDate().month
      }
    }).then(resp => {
      const dateFrom = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-01`).dateFormated
      const dateTo = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-${moment(`${getDate().year}-${getDate().month}`, "YYYY-MM").endOf('month').date()}`).dateFormated
      
      const targetAmount = Number(resp.data.totalGoalAmount)
      const amountAchieved = Number(resp.data.totalBrokerAchieved)

      const percentage = (amountAchieved / targetAmount) * 100;

      setGoals(resp.data)
      setData({
        amountAchieved: resp.data.totalBrokerAchieved,
        month: `${dateFrom} à ${dateTo}`,
        monthName: newFormatDate(`${getDate().year}-${getDate().month}-01`).month,
        observation: '',
        over: Number(resp.data.totalGoalAmount) - Number(resp.data.totalBrokerAchieved),
        percentage: percentage,
        targetAmount: resp.data.totalGoalAmount
      })
    }).catch(error => {
      returnError(error, 'Não conseguimos encontrar as metas!')
    })
  }

  function getGoalsBroker() {
    api.get(`/goals/broker/${userProfile.roleId}`, {
      params: {
        year: getDate().year,
        month: getDate().month,
        type: filterMetas
      }
    }).then(resp => {
      const dateFrom = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-01`).dateFormated
      const dateTo = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-${moment(`${getDate().year}-${getDate().month}`, "YYYY-MM").endOf('month').date()}`).dateFormated
      const goalsFiltered = resp.data.advisorMetrics.map((adv: any) => (
        {
          goalName: adv.goalName,
          brokerName: adv.advisorName,
          brokerDocument: adv.advisorDocument,
          targetAmount: adv.targetAmount,
          totalAchieved: adv.totalAchieved,
          percentAchieved: adv.percentAchieved
        }
      ))
      setGoals({
        results: goalsFiltered,
        totalBrokerAchieved: 0,
        totalGoalAmount: 0
      })
      setGoalBrokerId(resp.data.id)
      setData({
        amountAchieved: resp.data.totalAchieved,
        month: `${dateFrom} à ${dateTo}`,
        monthName: newFormatDate(`${getDate().year}-${getDate().month}-01`).month,
        observation: resp.data.observations,
        over: Number(resp.data.targetAmount) - Number(resp.data.totalAchieved),
        percentage: resp.data.percentAchieved,
        targetAmount: resp.data.targetAmount
      })
    }).catch(error => {
      returnError(error, 'Não conseguimos encontrar as metas!')
    })
  }

  function getGoalsAdvisor() {
    api.get(`/goals/advisor/${userProfile.roleId}`, {
      params: {
        ownerRelationId: userProfile.roleId,
        year: getDate().year,
        month: getDate().month
      }
    }).then(resp => {
      const dateFrom = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-01`).dateFormated
      const dateTo = newFormatDate(`${getDate().year}-${getDate().month.padStart(2, '0')}-${moment(`${getDate().year}-${getDate().month}`, "YYYY-MM").endOf('month').date()}`).dateFormated
      
      setGoals({
        totalBrokerAchieved: 0,
        totalGoalAmount: 0,
        results: [
          {
            goalName: resp.data.goalName,
            brokerName: resp.data.advisorName,
            brokerDocument: resp.data.advisorDocument,
            percentAchieved: resp.data.percentAchieved,
            perspectiveAmount: resp.data.perspectiveAmount,
            targetAmount: resp.data.targetAmount,
            totalAchieved: resp.data.totalAchieved,
          }
        ]
      })
      setData({
        amountAchieved: resp.data.totalAchieved,
        month: `${dateFrom} à ${dateTo}`,
        monthName: newFormatDate(`${getDate().year}-${getDate().month}-01`).month,
        observation: '',
        over: Number(resp.data.targetAmount) - Number(resp.data.totalAchieved),
        percentage: resp.data.percentAchieved,
        targetAmount: resp.data.targetAmount
      })
    }).catch(error => {
      returnError(error, 'Não conseguimos encontrar as metas!')
    })
  }

  const getAcessors = async () => {
    try {
      const {data} = await api.get('/wallets/broker/advisors', {
        params: {
          adviserId: userProfile.roleId,
        }
      })
      setAdvisors(data)
    } catch (error: any) {
      returnError(error, 'Não conseguimos encontrar as metas!')
    }
  }

  const getBrokers = async () => {
    try {
      const { data } = await api.get('/wallets/list-brokers')
      setAdvisors(data)
    } catch (error: any) {
      returnError(error, 'Não conseguimos encontrar as metas!')
    }
  }

  const returnTypePage = () => {
    if(addGoal) {
      return {
        title: 'Nova Meta',
        component: <GoalDashboard data={data} />,
        subtitle: <h2 className="text-2xl text-center">Adicionar Meta para {userProfile.name === 'admin' || userProfile.name === 'superadmin' ? 'Brokers' : 'Colaboradores'}</h2>,
        page: <AddGoals brokerAdvisor={advisors} setAddgoalModal={setAddGoal} brokerGoalId={goalBrokerId} golsData={date} />
      }
    } else if(addPerspective) {
      return {
        title: 'Nova Perspectiva',
        component: <div></div>,
        subtitle: <p></p>,
        page: <AddPerspective brokerGoalId={goalBrokerId} />
      }
    } else {
      return {
        title: 'Metas',
        component: <AdminDashboard data={data} />,
        subtitle: <h2 className="text-2xl text-center">{perspectiveSearch === 'perspective' ? `Captação da Perspectiva dos Brokers` : `Meta de Captação por ${userProfile.name === 'admin' || userProfile.name === 'superadmin' ? 'Brokers' : 'Colaborador'}`}</h2>,
        page: goals && <ProgressTable filteredType={perspectiveSearch} filters={[
          {
            label: perspectiveSearch === 'perspective' ? 'Perspectiva' : 'Meta atingida'
          },
          {
            label: perspectiveSearch === 'perspective' ? 'Meta Total' : 'Meta Restante'
          }
        ]} goals={goals.results} />
      }
    }
  }

  return (
    <div>
      <Header/>
      <Sidebar>
        <div className="text-white">
          <div className="w-full">
            <p className="text-2xl text-center">{returnTypePage().title}</p>
            <div className="flex items-center justify-start">
              <div className="relative">
              {
                !addGoal && !addPerspective && (
                  <div className='flex items-center p-2 rounded-lg bg-[#3A3A3A] cursor-pointer mr-5' onClick={(() => setModalFilter(!modalFilter))}>
                    <AdjustmentsHorizontalIcon width={15} color='#fff' />
                    <p className='px-2 text-sm'>Filtro</p>
                    {
                      modalFilter ? <ChevronUpIcon width={15} color='#fff' /> : <ChevronDownIcon width={15} color='#fff' />
                    }
                  </div>
                )
              }
            {
              (modalFilter && !addGoal && !addPerspective)  && (
                <div className='absolute w-[300px] bg-[#3A3A3A] p-5 top-10 rounded-tr-lg rounded-b-lg z-10'>
                  <div className='flex flex-col justify-between'>
                    <div className=''>
                      <p className='text-sm mb-1'>Data</p>
                      <input 
                        value={date} 
                        className='p-1 w-full rounded-md text-xs bg-transparent border' 
                        onChange={({target}) => {
                          setDate(target.value)
                        }} 
                        type="month" 
                      />
                    </div>
                    {
                      (userProfile.name === 'broker' || userProfile.name === 'superadmin') && (
                        <div className="my-2">
                          <Select selected={filterMetas} setSelected={setFilterMetas} options={options} />
                        </div>
                      )
                    }
                    <div className="mt-4">
                      <p className="bg-orange-linear px-5 py-2 rounded-xl text-center text-sm cursor-pointer select-none" onClick={() => {
                        toast.info('Filtrando resultados!')
                        handleSelectTypeGoals()
                        setModalFilter(false)
                        setPerspectiveSeach(filterMetas)
                      }}>Aplicar</p>
                    </div>
                  </div>
                </div>
              )
            }
              </div>
              {
                (userProfile.name === 'broker' || userProfile.name === 'superadmin' ) && !addPerspective && (
                  <div>
                    <Button variant={!addGoal ? 'default' : 'secondary'} onClick={() => setAddGoal(!addGoal)}>
                      {!addGoal ? 'Adicionar Meta' : 'Cancelar'}
                    </Button>
                  </div>
                )
              }
              {
                (userProfile.name === 'broker' && !addGoal) && (
                  // <div >
                  //   <p className="bg-orange-linear px-5 py-2 rounded-xl text-sm cursor-pointer" >{!addPerspective ? 'Adicionar Perspectiva' : 'Cancelar'}</p>
                  // </div>
                  <Button variant={!addPerspective ? 'default' : 'secondary'} className={!addPerspective ? "ml-4" : ''} onClick={() => setAddPerspective(!addPerspective)}>
                    {!addPerspective ? 'Adicionar Perspectiva' : 'Cancelar'}
                  </Button>
                )
              }
            </div>
            {
              returnTypePage().component
            }

            <div className="my-5">
              {
                returnTypePage().subtitle
              }
            </div>
            <div>
              {
                returnTypePage().page
              }
            </div>
          </div>
        </div>
      </Sidebar>
    </div>
  )
}