import Button from "@/components/Button/Button"
import StatusWithDescription from "@/components/StatusWithDescription"
import formatDate from "@/functions/formatDate"
import { formatStatusContract } from "@/functions/formatStatus"
import Contact from "@/models/contract"
import { clearLetters, cnpjMask, cpfMask } from "@/utils/masks"
import { DocumentTextIcon } from "@heroicons/react/24/outline"
import moment from "moment"

interface IProps {
  contract: Contact
  setRenew: (d:any) => void
  setModal: (d:any) => void
  setModalPayment: (d:any) => void
  setAditive: (d:any) => void
  loading: boolean
  resendContract: () => void
}

export default function ContractData({contract, setAditive, setModal, setModalPayment, setRenew, loading, resendContract}: IProps) {
  return (
    <div>
        <div className='flex flex-col gap-y-3 mt-10 w-full'>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>ID</p>
            <p className='text-xs text-end'>{contract?.idContrato}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Investidor</p>
            <p className='text-xs text-end'>{contract?.nomeInvestidor}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>CPF/CNPJ</p>
            <p className='text-xs text-end'>{contract?.documentoInvestidor !== undefined && clearLetters(contract?.documentoInvestidor)?.length <= 11 ? cpfMask(String(contract?.documentoInvestidor)) : cnpjMask(String(contract?.documentoInvestidor))}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Consultor Responsável</p>
            <p className='text-xs text-end'>{contract?.consultorResponsavel}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Comprado com</p>
            <p className='text-xs text-end'>{contract?.compradoCom}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Assinaturas</p>
            <p className='text-xs text-end'>{contract?.consultorResponsavel}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Valor</p>
            <p className='text-xs text-end'>
            {
              Number(contract?.valorInvestimento).toLocaleString('pt-br', {
                style: 'currency',
                currency: 'BRL'
              })
            }
            </p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Contrato Solicitado em</p>
            <p className='text-xs text-end'>{formatDate(contract?.inicioContrato)}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Final do contrato</p>
            <p className='text-xs text-end'>{formatDate(contract?.fimContrato)}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Status de Contrato</p>
            <div>
              <StatusWithDescription 
                description={formatStatusContract(contract.statusContrato).description}
                text={formatStatusContract(contract.statusContrato).title}
                textColor={formatStatusContract(contract.statusContrato).title}
              />
            </div>
          </div>
          {/* <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Status da Assinatura</p>
            <p className='text-xs text-end'>{contract?.statusAssinatura || 'Não encontrado'}</p>
          </div> */}
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Qtd de Cotas/Participações</p>
            <p className='text-xs text-end'>{contract?.cotas || 'Não encontrado'}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Data de Carência</p>
            <p className='text-xs text-end'>{contract?.periodoCarencia}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Porcentagens</p>
            <p className='text-xs text-end'>{contract?.rendimentoInvestimento}%</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Ativado em</p>
            <p className='text-xs text-end'>{formatDate(contract?.inicioContrato)}</p>
          </div>
          <div className='flex w-full'>
            <p className='flex-1 font-bold text-xs'>Tags</p>
            <p className='text-xs text-end'>#{contract?.tags === 'P2P' ? 'Mútuo' : contract?.tags || 'NE'}</p>
          </div>
          {
            contract.contratoPdf && (
              <div className='flex w-full mt-3'>
                <p className='flex-1 font-bold text-sm text-[#FF9900] cursor-pointer' onClick={() => window.open(contract.contratoPdf, "_blank")}>Ver contrato</p>
              </div>
            )
          }
          {
            contract.comprovamentePagamento && (
              <div className='flex w-full mt-3'>
                <p className='flex-1 font-bold text-sm text-[#FF9900] cursor-pointer' onClick={() => window.open(contract.comprovamentePagamento, "_blank")}>Ver comprovante anexado</p>
              </div>
            )
          }
        </div>
    </div>
  )
}