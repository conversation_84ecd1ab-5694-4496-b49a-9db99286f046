'use client';

import Button from '@/components/Button/Button';
import FilterModal from '@/components/FilterModal';
import Header from '@/components/Header';
import InputSearch from '@/components/InputSearch';
import Sidebar from '@/components/Sidebar';
import Table from '@/components/Table';
import TableFormat from '@/components/Table/components/TableFormat';
import api from '@/core/api';
import returnError from '@/functions/returnError';
import formatValue from '@/utils/formatValue';
import { clearLetters, cnpjMask, cpfMask } from '@/utils/masks';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Skeleton from '@/components/Skeleton/intex';
import { CheckIcon } from '@heroicons/react/24/outline';
import { useNavigation } from '@/hooks/navigation';
import { IncomeReport } from './types';
import paginate from '@/functions/pagination';
import { formatStatusIncomePayment } from '@/functions/formatStatus';
import { getUserProfile } from '@/functions/getUserData';
import Select from '@/components/Select';

export default function InformeRendimentos() {
  const [oldIncomeReports, setOldIncomeReports] = useState<IncomeReport[]>([]);
  const [incomeReports, setIncomeReports] = useState<IncomeReport[] | any>([]);
  const [openFilterModal, setOpenFilterModal] = useState(false);
  const { navigation } = useNavigation();

  const [loading, setLoading] = useState(false);
  const [loadingButtonSend, setLoadingButtonSend] = useState(false);
  const [startDate, setStartDate] = useState<String>(
    String(moment().subtract(1, 'year').year() || ''),
  );
  const [page, setPage] = useState(1);
  const [document, setDocument] = useState('');
  const [optSelected, setOptSelected] = useState('');
  const [checkedItems, setCheckedItems] = useState(false);

  const [arraySelected, setArraySelected] = useState<string[]>([]);
  const [arraySendedIR, setArraySendedIR] = useState<string[]>([]);

  const userProfile = getUserProfile();

  const [pagination, setPagination] = useState({
    total: 1,
    lastPage: 1,
    perPage: 10,
  });

  const paymentsFilter = [
    {
      title: 'Todos',
      value: '',
    },
    {
      title: 'Pendentes',
      value: 'PENDENT',
    },
    {
      title: 'Envio em andamento',
      value: 'DELIVERED',
    },
    {
      title: 'Enviados',
      value: 'SEND',
    },
    {
      title: 'Não enviados',
      value: 'ERROR',
    },
  ];

  useEffect(() => {
    getIncomeReport();
  }, [optSelected]);

  useEffect(() => {
    const { items, lastPage, totalItems } = paginate({
      items: oldIncomeReports,
      page,
      perPage: 10,
    });

    setIncomeReports(items);
    setPagination({
      lastPage,
      perPage: 10,
      total: totalItems,
    });
  }, [page, oldIncomeReports]);

  const returnRouteUserProfile = () => {
    switch (userProfile?.name) {
      case 'superadmin':
        return 'income-report/investors';
      case 'broker':
        return 'broker/income-report/investors';
      case 'advisor':
        return 'advisor/income-report/investors';
      default:
        return '';
    }
  };

  const getIncomeReport = () => {
    setLoading(true);
    api
      .get(returnRouteUserProfile(), {
        params: {
          referenceYear: Number(startDate),
          document: document === '' ? undefined : clearLetters(document),
          status: optSelected === '' ? undefined : optSelected,
        },
      })
      .then((resp) => {
        setPage(1)
        setOldIncomeReports(resp.data);
        const { items, lastPage, totalItems } = paginate({
          items: resp.data,
          page: 1,
          perPage: 10,
        });
        setIncomeReports(items);
        setPagination({
          lastPage: lastPage,
          perPage: 10,
          total: totalItems,
        });
      })
      .catch((err) => {
        returnError(err, 'Não foi possivel listar os pagamentos.');
      })
      .finally(() => setLoading(false));
  };

  const anoAtual = moment().year();
  const anos = Array.from({ length: anoAtual - 2022 }, (_, i) => {
    const ano = (2022 + i).toString();
    return { label: ano, value: ano };
  });

  const sendIncomeReports = () => {
    if (arraySelected.length === 0) {
      return toast.warning(
        'Selecione o informe de rendimento que quer enviar ao investidor',
      );
    }

    setLoadingButtonSend(true);

    const payload = {
      investorIds: arraySelected,
      year: Number(startDate),
    };

    api
      .post('income-report/generate-investors', payload)
      .then((resp) => {
        toast.success(
          'Informe de rendimentos encaminhado aos investidores com sucesso!',
        );
        setArraySelected([]);
        setArraySendedIR(arraySelected)
        setCheckedItems(false);
        getIncomeReport();
      })
      .catch((err) => {
        returnError(
          err,
          'Tivemos um erro ao enviar os informes de rendimentos selecionados.',
        );
      })
      .finally(() => setLoadingButtonSend(false));
  };

  return (
    <div>
      <Header />
      <Sidebar>
        <div>
          <div>
            <p className="text-white text-center mb-8 text-3xl">
              Informe de Rendimentos
            </p>
            <div className="flex flex-col md:flex-row justify-between m-auto w-full gap-4 md:w-[1100px]">
              <div className="md:w-[40%] py-4 rounded-lg border-[#FF9900] border">
                <div className="m-auto text-center flex flex-col items-center relative">
                  <p className="text-white mb-5 md:text-base text-sm">
                    Informes Enviados
                  </p>
                  <Skeleton loading={loading} height="65px" width="50px">
                    <p className="text-white mb-5 md:text-7xl font-semibold text-3xl">
                      {oldIncomeReports.filter((item) => item.status === 'SEND')
                        ?.length || 0}
                    </p>
                  </Skeleton>
                </div>
              </div>
              <div className="md:w-[40%] py-4 rounded-lg border-[#FF9900] border">
                <div className="m-auto text-center flex flex-col items-center relative">
                  <p className="text-white mb-5 md:text-base text-sm">
                    Informes Pendentes
                  </p>
                  <Skeleton loading={loading} height="65px" width="50px">
                    <p className="text-white mb-5 md:text-7xl font-semibold text-3xl">
                      {oldIncomeReports.filter(
                        (item) => item.status === 'PENDENT',
                      )?.length || 0}
                    </p>
                  </Skeleton>
                </div>
              </div>
            </div>
          </div>
          <div className="flex bg-[#1C1C1C] p-2 rounded-md m-auto mt-5 text-white w-full md:w-fit flex-wrap md:flex-nowrap gap-2">
            {paymentsFilter.map((opt, index) => (
              <div
                onClick={() => {
                  setOptSelected(opt.value);
                  setPage(1);
                }}
                className={`hover:bg-[#313131] px-2 py-3 rounded-md cursor-pointer text-center flex items-center ${
                  optSelected === opt.value ? 'bg-[#313131]' : ''
                }`}
                key={index}
              >
                <p
                  className={`${
                    optSelected === opt.value ? 'text-[#FF9900]' : ''
                  } text-xs`}
                >
                  {opt.title}
                </p>
              </div>
            ))}
          </div>
          <TableFormat>
            <div className="w-full p-2 flex justify-end gap-4">
              <div className="flex flex-1 w-full justify-between items-center gap-4">
                <div className="md:w-4/12">
                  <InputSearch
                    handleSearch={getIncomeReport}
                    placeholder="Pesquisar por CPF/CNPJ"
                    setValue={(e) => {
                      if (clearLetters(e).length <= 11) {
                        setDocument(cpfMask(e));
                      } else {
                        setDocument(cnpjMask(e));
                      }
                    }}
                    value={document}
                  />
                </div>
                <div>
                  <Button
                    label="Enviar"
                    className="bg-orange-linear"
                    loading={loadingButtonSend}
                    disabled={loadingButtonSend || arraySelected.length === 0}
                    size="sm" 
                    handleSubmit={() => {
                      sendIncomeReports()
                    }}
                  />
                </div>
              </div>
              <FilterModal
                activeModal={openFilterModal}
                setActiveModal={setOpenFilterModal}
                handleSearch={() => {
                  getIncomeReport();
                }}
              >
                <div className="flex justify-between mt-2">
                  <div className="w-full">
                    <Select
                      selected={String(startDate)}
                      setSelected={(e) => {
                        setStartDate(e);
                      }}
                      options={anos}
                    />
                  </div>
                </div>
              </FilterModal>
            </div>
            <Table
              loading={loading}
              pagination={{
                lastPage: pagination.lastPage,
                page,
                perPage: 10,
                setPage,
                totalItems: String(pagination.total),
              }}
              selectable={true}
              checked={checkedItems}
              setChecked={(isSelected) => {
                if (isSelected) {
                  const newArraySelected = oldIncomeReports
                    .filter((item) => item.status === 'PENDENT')
                    .map((item) => item.id);
                  setArraySelected(newArraySelected);
                } else {
                  setArraySelected([]);
                }
                setCheckedItems(isSelected);
              }}
              data={incomeReports || []}
              headers={[
                {
                  title: '',
                  component: 'select',
                  render: (p, row) => {
                    const checked = arraySelected.includes(row.id);
                    return (
                      <div>
                        {row.status === 'PENDENT' && (
                          <div
                            className="w-5 h-5 border rounded-md border-[#FF9900] cursor-pointer flex items-center justify-center"
                            onClick={() => {
                              setArraySelected((prev) =>
                                checked
                                  ? prev.filter((id) => id !== row.id)
                                  : [...prev, row.id],
                              );
                            }}
                          >
                            {checked && (
                              <CheckIcon width={20} color="#1EF97C" />
                            )}
                          </div>
                        )}
                      </div>
                    );
                  },
                },
                {
                  title: 'Nome',
                  component: 'name',
                },
                {
                  title: 'CPF/CNPJ',
                  component: 'document',
                  render: (value) => (
                    <p>
                      {String(value).length <= 11
                        ? cpfMask(String(value))
                        : cnpjMask(String(value))}
                    </p>
                  ),
                },
                {
                  title: 'E-mail',
                  component: 'email',
                },
                {
                  title: 'Status',
                  component: 'status',
                  width: '140px',
                  render: (value, row) => {
                    const sended = arraySendedIR.find(sendedId => row.id === sendedId)

                    const { textColor, title, borderColor } =
                      formatStatusIncomePayment(sended ? 'DELIVERED' : value);

                      console.log(sended)
                    return (
                      <div>
                        <p
                          className={`text-xs font-bold select-none ${
                            value !== 'ERROR' ? textColor : 'text-[#F10303]'
                          } border ${
                            value !== 'ERROR' ? borderColor : 'border-[#F10303]'
                          } text-center p-1 rounded-md`}
                        >
                          {title}
                        </p>
                      </div>
                    );
                  },
                },
                {
                  title: '',
                  component: 'id',
                  width: '120px',
                  render: (value, row) => (
                    <p
                      className={`${
                        row.status !== 'PENDENT'
                          ? 'text-[#42A5F5]'
                          : 'text-zinc-500'
                      } font-bold text-center text-xs cursor-pointer`}
                      onClick={() => {
                        if (row.status !== "PENDENT") {
                          navigation(
                            `/informe-rendimentos/${value}/${startDate}`,
                          );
                        }
                      }}
                    >
                      Ver mais
                    </p>
                  ),
                },
              ]}
            />
          </TableFormat>
        </div>
      </Sidebar>
    </div>
  );
}
