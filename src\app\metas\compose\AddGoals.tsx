import SelectSearch from "@/components/SelectSearch"
import { IAdvisor } from "../page"
import { useState } from "react"
import Input from "@/components/Input"
import { valueMask } from "@/utils/masks"
import moment from "moment"
import { toast } from "react-toastify"
import { UserProfile } from "@/models/user"
import api from "@/core/api"
import { TrashIcon } from "@heroicons/react/24/outline"
import { getUserProfile } from "@/functions/getUserData"
import { Button } from "@/components/ui/button"

interface IProps {
  brokerAdvisor: IAdvisor[] | undefined,
  setAddgoalModal: (d: any) => void
  golsData: string
  brokerGoalId?: string
}

interface IAddGoal {
  name: string
  ownerRelationId: string
  targetAmount: string
  dateTo: string,
  dateFrom: string
  observations: string
  brokerOwnerRelationId?: string
  adminOwnerRelationId?: string
  brokerGoalId?: string
}

export default function AddGoals({brokerAdvisor, setAddgoalModal, golsData, brokerGoalId}:IProps) {
  const [brokers, setBrokers] = useState<IAdvisor[] | any>(brokerAdvisor)
  const [value, setValue] = useState('')
  const [addGoal, setAddGoal] = useState<IAddGoal[]>([])
  const [loading, setLoading] = useState(false)

  const userProfile = getUserProfile()

  function sendData() {
    setLoading(true)
    const payload = addGoal.map(goal => {
      return {
        ...goal,
        targetAmount: Number(goal.targetAmount.replaceAll('.', '').replace(',', '.'))
      }
    })

    api.post(`/goals/${userProfile.name === 'superadmin' ? 'broker' : 'advisor'}`, payload).then(resp => {
      toast.success('Metas cadastrada com sucesso!')
      setAddgoalModal(false)
    }).catch(error => {
      toast.error(error.response.data.message || 'Não foi possivel cadastrar as metas')
    }).finally(() => setLoading(false))
  }

  return (
    <div>
      <div>
      <div className='md:w-3/4 mb-5 m-auto'>
      {
        brokerAdvisor && (
          <SelectSearch selectable={true} label="" items={brokers} value={value} setValue={(a) => {
            const userId = String(a).split(' - ')[1] || ''
            const userName = String(a).split(' - ')[0] || ''
            const year = golsData ? golsData.split('-')[0] : String(moment().year())
            const month = golsData ? golsData.split('-')[1] :  String(moment().month() + 1)

            const filteredBrokers = brokers.filter((broker: IAdvisor) => broker.id !== userId)
            setBrokers(filteredBrokers)
            const dateFrom = `${year}-${month.padStart(2, '0')}-01`
            const dateTo = `${year}-${month.padStart(2, '0')}-${moment(`${year}-${month}`, "YYYY-MM").endOf('month').date()}`
            setAddGoal([
              ...addGoal,
              {
                ownerRelationId: userId,
                name: userName,
                dateFrom,
                dateTo,
                observations: '',
                targetAmount: '',
                adminOwnerRelationId: userProfile.name === 'superadmin' ? userProfile.roleId : undefined,
                brokerGoalId: userProfile.name === 'broker' ? brokerGoalId : undefined
              }
            ])
          }} />
        )
      }
      </div>
      </div>
      <div className="flex gap-6">
        <div className="flex items-center">
          <div className="w-[20px] h-[20px] bg-orange-linear" />
          <p className="ml-1 text-sm">Meta Atingida</p>
        </div>
        <div className="flex items-center">
          <div className="w-[20px] h-[20px] bg-[#3A3A3A]" />
          <p className="ml-1 text-sm">Meta Restante</p>
        </div>
      </div>
      <div>
        {
          addGoal.map(goal => (
            <div key={goal.ownerRelationId} className="text-white my-5 flex items-center">
              <p className="w-3/12">{goal.name}</p>
              <div className="flex-1 rounded-lg ml-10 h-9 flex items-center gap-4">
                <div>
                  <Input id="" label="" placeholder="R$" name="" type="text" value={goal.targetAmount} onChange={(e) => {
                    const goalFind = addGoal.filter(adg => adg.ownerRelationId !== goal.ownerRelationId)
                    setAddGoal([
                      ...goalFind,
                      {
                        ...goal,
                        targetAmount: valueMask(e.target.value)
                      }
                    ])
                  }} />
                </div>
                <div className="w-7/12">
                  <Input id="" label="" placeholder="Observações" name="" type="text" value={goal.observations} onChange={(e) => {
                    const goalFind = addGoal.filter(adg => adg.ownerRelationId !== goal.ownerRelationId)
                    setAddGoal([
                      ...goalFind,
                      {
                        ...goal,
                        observations: e.target.value
                      }
                    ])
                  }} />
                </div>
                {/* <div className='bg-red-500 cursor-pointer text-white p-1 rounded-full' onClick={() => {
                  const filtered = addGoal.filter(g => g.ownerRelationId !== goal.ownerRelationId)
                  setBrokers(filteredBrokers)
                  setAddGoal(filtered)
                }}>
                  <TrashIcon width={20} />
                </div> */}
              </div>
            </div>
          ))
        }
      </div>
      <div className="flex w-full justify-end mt-10 mb-5">
        <div>
          <Button onClick={sendData} loading={loading}>
            Concluir
          </Button>
        </div>
      </div>
    </div>
  )
}