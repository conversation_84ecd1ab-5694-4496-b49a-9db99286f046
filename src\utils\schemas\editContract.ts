import * as yup from "yup";

export const editPFContract = yup
  .object()
  .shape({
    isSCP: yup.boolean(),
    name: yup
      .string()
      .min(3, "O nome deve ter no mínimo 3 caracteres")
      .required("Campo obrigatório"),
    document: yup.string().required("Campo obrigatório"),
    email: yup
      .string()
      .email("E-mail incorreto")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
      .required("Campo obrigatório"),
    rg: yup
      .string()
      .min(3, "RG deve ter no mínimo 3 caracteres")
      .required("Campo obrigatório"),
    phoneNumber: yup
      .string()
      .max(15, "Número de telefone inválido!")
      .required("Campo obrigatório"),
    dtBirth: yup.string().required("Campo obrigatório"),
    motherName: yup
      .string()
      .min(3, "Nome da mãe deve conter no mínimo 3 caracteres")
      .required("Campo obrigatório"),

    // Endereço
    zipCode: yup.string().required("Obrigatório"),
    neighborhood: yup.string().required("Obrigatório"),
    state: yup
      .string()
      .min(2, "Estado inválido!")
      .max(2, "Estado inválido!")
      .required("Obrigatório"),
    city: yup.string().required("Obrigatório"),
    complement: yup.string().default(""),
    number: yup.string().required("Obrigatório"),
    street: yup.string().required("Obrigatório"),

    // Investimento
    value: yup.string().required("Obrigatório"),
    term: yup.string().required("Obrigatório"),
    yield: yup
      .number()
      .required("Obrigatório")
      .min(0, "O valor mínimo é 0")
      .max(5, "O valor máximo é 5")
      .typeError("O valor deve ser um número válido"),
    purchasedWith: yup.string().required("Obrigatório"),

    initDate: yup.string().required("Obrigatório"),
    endDate: yup.string().required("Obrigatório"),

    // Conditional schemas
    issuer: yup.string().required("Campo obrigatório"),
    placeOfBirth: yup.string().required("Campo obrigatório"),
    occupation: yup.string().required("Campo obrigatório"),
    amountQuotes: yup.string().when("isSCP", (isSCP, schema) => {
      if (isSCP[0] === true) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),

    documentPdf: yup.string().notRequired(),
    proofPayment: yup.string().notRequired(),
    proofOfResidence: yup.string().notRequired(),
    contract: yup.string().notRequired(),
  })
  .required();

export const editPJContract = yup
  .object()
  .shape({
    isSCP: yup.boolean(),

    // Dados Representante
    email: yup
      .string()
      .email("E-mail incorreto")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "E-mail inválido")
      .required("Campo obrigatório"),
    phoneNumber: yup
      .string()
      .max(15, "Número de telefone inválido!")
      .required("Campo obrigatório"), // (69) 99959-8313
    dtBirth: yup.string().required("Campo obrigatório"),
    rg: yup
      .string()
      .min(3, "RG deve ter no mínimo 3 caracteres")
      .required("Obrigatório"),
    ownerName: yup
      .string()
      .min(3, "O nome deve ter no mínimo 3 caracteres")
      .required("Campo obrigatório"),
    ownerDocument: yup
      .string()
      .min(3, "O nome da mãe deve ter no mínimo 3 caracteres")
      .required("Campo obrigatório"),
    motherName: yup.string().required("Campo obrigatório"),
    issuer: yup.string().required("Campo obrigatório"),
    placeOfBirth: yup.string().required("Campo obrigatório"),
    occupation: yup.string().required("Campo obrigatório"),

    // Dados Empresa
    name: yup
      .string()
      .min(3, "O nome deve ter no mínimo 3 caracteres")
      .required("Campo obrigatório"),
    document: yup.string().required("Campo obrigatório"),
    companyType: yup.string().required("Obrigatório"),
    // dtOpening: yup.string().required('Campo oobrigatório'),

    // Endereço Representante
    zipCode: yup.string().required("Obrigatório"),
    neighborhood: yup.string().required("Obrigatório"),
    state: yup
      .string()
      .min(2, "Estado inválido!")
      .max(2, "Estado inválido!")
      .required("Obrigatório"),
    city: yup.string().required("Obrigatório"),
    complement: yup.string().default(""),
    number: yup.string().required("Obrigatório"),
    street: yup.string().required("Obrigatório"),

    // Investimento
    value: yup.string().required("Obrigatório"),
    term: yup.string().required("Obrigatório"),
    yield: yup
      .number()
      .required("Obrigatório")
      .min(0, "O valor mínimo é 0")
      .max(5, "O valor máximo é 5")
      .typeError("O valor deve ser um número válido"),
    purchasedWith: yup.string().required("Obrigatório"),
    initDate: yup.string().required("Obrigatório"),
    endDate: yup.string().required("Obrigatório"),
    // profile: yup.string().required('Obrigatório'),

    amountQuotes: yup.string().when("isSCP", (isSCP, schema) => {
      if (isSCP[0] === true) {
        return schema.required("Campo obrigatório");
      } else {
        return schema.notRequired();
      }
    }),

    // Endereço Empresa
    companyNumber: yup.string().required("Campo obrigatório"),
    companyComplement: yup.string().default(""),
    companyCity: yup.string().required("Campo obrigatório"),
    companyState: yup
      .string()
      .min(2, "Estado inválido!")
      .max(2, "Estado inválido!")
      .required("Campo obrigatório"),
    companyStreet: yup.string().required("Campo obrigatório"),
    companyZipCode: yup.string().required("Campo obrigatório"),
    companyNeighborhood: yup.string().required("Campo obrigatório"),

    documentPdf: yup.string().notRequired(),
    proofPayment: yup.string().notRequired(),
    proofOfResidence: yup.string().notRequired(),
    contract: yup.string().notRequired(),
  })
  .required();
