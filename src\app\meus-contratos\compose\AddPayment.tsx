import Button from "@/components/Button/Button";
import Dropzone from "@/components/Dropzone";
import api from "@/core/api";
import Contact from "@/models/contract";
import { useState } from "react";
import { toast } from "react-toastify";

interface IProps {
  contract: Contact
  setOpenModal: (d: any) => void
  getContracts: (d: string) => void
  documentSearch: string
}

export default function AddPayment({ contract, setOpenModal, documentSearch, getContracts }: IProps) {
  const [newContract, setNewContract] = useState<FileList>()
  const [loading, setLoading] = useState(false)
  const [contractName, setContractName] = useState<string>()
  const renewContract = async () => {
    if(!newContract) {
      return toast.warning('Selecione um comprovante para anexar!')
    }
    setLoading(true)
    const form = new FormData()
    form.append('contractId', contract.idContrato)
    if (newContract) {
      form.append('file', newContract[0])
    }
    api.patch(`/contract/upload/proof-payment`, form).then(resp => {
      toast.success('Comprovante anexado com sucesso!')
      setLoading(false)
      setOpenModal(false)
      setTimeout(() => {
        window.location.reload()
      }, 1000);
    }).catch(err => {
      toast.error(err?.response?.data?.message || "Não foi possivel anexar o comprovante a esse contrato")
      setLoading(false)
    })
  }
  
  const handleRemoveFile = () => {
    setNewContract(undefined);
  }
  return (
    <div className="fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10">
      <div className="md:w-3/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]">
        <p className="text-lg font-bold">Anexar comprovante</p>
        <p className="text-xs">*Anexe neste campo o comprovante de Pagamento do seu Investidor</p>

        <div className='flex gap-2 mb-10 text-white items-start justify-center mt-5'>
          <div>
            <p className='mb-1'>Comprovante</p>
            <Dropzone onFileUploaded={setNewContract} 
            fileName={contractName}
              onRemoveFile={handleRemoveFile}
            />
          </div>
        </div>
        <div className='flex w-full mt-10 justify-between gap-10'>
          <Button 
            label="Anexar"
            loading={loading}
            className="bg-orange-linear"
            handleSubmit={renewContract}
          />
          <div className='px-5 py-2 bg-[#313131] cursor-pointer flex items-center' onClick={() => setOpenModal(false)}>
            <p className='text-sm'>Fechar</p>
          </div>
        </div>
      </div>
    </div>
  )
}