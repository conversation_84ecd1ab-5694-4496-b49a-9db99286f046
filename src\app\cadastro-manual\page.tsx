/* eslint-disable @next/next/no-img-element */
"use client"

import api from '@/core/api'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { Regions } from '@/functions/RegionsEnum'
import Input from '@/components/Input'
import { useRouter } from 'next/router'
import { useFormik } from 'formik'
import { useForm, SubmitHandler } from "react-hook-form"
import axios from 'axios'
import { schemaRegisterManual } from '@/utils/schemas/schemasValidation'
import Header from '@/components/Header'
import Sidebar from '@/components/Sidebar'
import User, { UserProfile } from '@/models/user'
import { clearLetters, cnpjMask, cpfMask, valueMask } from '@/utils/masks'
import Dropzone from '@/components/Dropzone'
import CreateInvestor from './compose/CreateInvestor'
import CreateBroker from './compose/CreateBroker'
import { getUserProfile } from '@/functions/getUserData'

export default function CadastroManual() {
  const [selected, setSelected] = useState<'investor' | 'broker' | 'acessor' | 'admin'>()

  const userProfile = getUserProfile()

  useEffect(() => {
    if (userProfile.name === 'admin') {
      setSelected('broker')
    } else {
      setSelected('investor')
    }
  }, [])

  const ReturnCreatePage = () => {
    switch (selected) {
      case 'investor':
        return <CreateInvestor />
      case 'broker':
        return <CreateBroker typeCreate='broker' />
      case 'acessor':
        return <CreateBroker typeCreate='advisor' />
      case 'admin':
        return <CreateBroker typeCreate='admin' hide />
      default:
        return <div></div>
    }
  }

  return (
    <>
      <Header />
      <Sidebar>
        <div className='w-full'>
          <div className='text-white flex'>
            {
              userProfile.name !== 'admin' && (
                <div onClick={() => setSelected('investor')} className={`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${selected === 'investor' ? 'bg-zinc-700' : ''}`}>
                  <p>Investidor</p>
                </div>
              )
            }
            {
              userProfile.name !== 'broker' && (
                <div onClick={() => setSelected('broker')} className={`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${selected === 'broker' ? 'bg-zinc-700' : ''}`}>
                  <p>Broker</p>
                </div>
              )
            }
            <div onClick={() => setSelected('acessor')} className={`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${selected === 'acessor' ? 'bg-zinc-700' : ''}`}>
              <p>Assessor</p>
            </div>
            {
              userProfile.name === 'superadmin' && (
                <div onClick={() => setSelected('admin')} className={`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${selected === 'admin' ? 'bg-zinc-700' : ''}`}>
                  <p>Admin / Gestor de carteiras</p>
                </div>
              )
            }
          </div>
          <div className='border rounded-tr-2xl rounded-b-2xl p-2 border-zinc-700'>
            {
              ReturnCreatePage()
            }
          </div>
        </div>
      </Sidebar>
    </>

  )
}
