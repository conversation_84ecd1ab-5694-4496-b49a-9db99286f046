"use client"

import InputText from "@/components/Inputs/InputText";
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm, SubmitHandler } from 'react-hook-form'
import { schemaRegister } from "@/utils/schemas/schemasValidation";
import Button from "@/components/Button/Button";
import { cepMask, clearLetters, cnpjMask, cpfMask, phoneMask, valueMask } from "@/utils/masks";
import { createPJContract } from "@/utils/schemas/createContract";
import { useEffect, useState } from "react";
import InputSelect from "@/components/Inputs/InputSelect";
import InputTextArea from "@/components/Inputs/InputTextArea";
import axios from "axios";
import { UserProfile } from "@/models/user";
import { signIca } from "@/constants/signIca";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import { toast } from "react-toastify";
import SelectSearch from "@/components/SelectSearch";
import Input from "@/components/Input";
import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import CoverForm from "@/components/CoverForm";
import { getFinalDataWithMount } from "@/functions/getDataFilter";
import moment from "moment";
import momentTz from "moment-timezone";
import formatNumberValue from "@/utils/formatNumberValue";
import { isValidateCNPJ, isValidateCPF } from "@/utils/validate-documents";
import { getUserProfile } from "@/functions/getUserData";
import validatePhoneNumber from "@/utils/validatePhoneNumber";
import { isUnderage } from "@/utils/isUnderage";
import isValidUF from "@/utils/isValidUf";
import { InferType, number } from "yup";
import Dropzone from "@/components/Dropzone";
import { ContractPJ } from "@/app/contratos/contrato/types";
import { editPJContract } from "@/utils/schemas/editContract";
import { useParams } from "next/navigation";
import { getZipCode } from "@/functions/getZipCode";
import { formatDateToEnglishType } from "@/functions/formatDate";
import formatValue, { cleanValue } from "@/utils/formatValue";
import { useNavigation } from "@/hooks/navigation";

interface IProps {
  modalityContract: 'mutuo' | 'scp'
  contractData?: ContractPJ
}

const companyTypes = [
  {
    label: 'MEI',
    value: 'MEI'
  },
  {
    label: 'EI',
    value: 'EI'
  },
  {
    label: 'EIRELI',
    value: 'EIRELI'
  },
  {
    label: 'SLU',
    value: 'SLU'
  },
  {
    label: 'LTDA',
    value: 'LTDA'
  },
  {
    label: 'SA',
    value: 'SA'
  },
  {
    label: 'SS',
    value: 'SS'
  },
  {
    label: 'CONSORCIO',
    value: 'CONSORCIO'
  },
]


type PJContractFields = keyof InferType<typeof editPJContract>

interface FieldReason {
  field: PJContractFields
  reason: string
}


export default function BusinessEditing({ modalityContract, contractData }: IProps) {
  const { id } = useParams()
  const [loading, setLoading] = useState(false)
  const [zipCode, setZipCode] = useState('')
  const [companyZipCode, setCompanyZipCode] = useState('')
  const [startDate, setStartDate] = useState('')
  const [investDate, setInvestDate] = useState('')

  const [contract, setContract] = useState<FileList>()
  const [file, setFile] = useState<FileList>()
  const [document, setDocumet] = useState<FileList>()
  const [residence, setResidence] = useState<FileList>()
  const {navigation} = useNavigation()

  const [reasons, setReasons] = useState<FieldReason[]>([])

  const userProfile = getUserProfile()

  const {
    register,
    handleSubmit,
    watch,
    setError,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(editPJContract),
    mode: "onChange"
  })

  function durationInMonths() {
    const start = moment(contractData?.investment.start);
    const end = moment(contractData?.investment.end);
  
    return String(end.diff(start, 'months'));
  }

  useEffect(() => {
    if(contractData?.investor) {
      // Dados da empresa
      setValue("name", contractData.investor.companyName)
      setValue("document", contractData.investor.document)
      setValue("companyType", contractData.investor.businessType)
      setValue("companyZipCode", contractData.investor.address.zipcode)
      setValue("companyNeighborhood", contractData.investor.address.neighborhood)
      setValue("companyStreet", contractData.investor.address.street)
      setValue("companyCity", contractData.investor.address.city)
      setValue("companyState", contractData.investor.address.state)
      setValue("companyNumber", contractData.investor.address.number)
      setValue("companyComplement", contractData.investor.address.complement)

      // Dados representante
      setValue("ownerName", contractData.investor.responsibleOwner.name)
      setValue("ownerDocument", contractData.investor.responsibleOwner.document)
      setValue("rg", contractData.investor.responsibleOwner.rg)
      setValue("issuer", contractData.investor.responsibleOwner.issuingAgency)
      setValue("placeOfBirth", contractData.investor.responsibleOwner.nationality)
      setValue("occupation", contractData.investor.responsibleOwner.occupation)
      setValue("phoneNumber", phoneMask(contractData.investor.responsibleOwner.phone))
      setValue("dtBirth", contractData.investor.responsibleOwner.birthDate)
      setValue("email", contractData.investor.responsibleOwner.email)
      setValue("motherName", contractData.investor.responsibleOwner.motherName)
      setValue("zipCode", contractData.investor.responsibleOwner.address.zipcode)
      setValue("neighborhood", contractData.investor.responsibleOwner.address.neighborhood)
      setValue("street", contractData.investor.responsibleOwner.address.street)
      setValue("city", contractData.investor.responsibleOwner.address.city)
      setValue("state", contractData.investor.responsibleOwner.address.state)
      setValue("number", contractData.investor.responsibleOwner.address.number)
      setValue("complement", contractData.investor.responsibleOwner.address.complement)

      setValue("value", String(cleanValue(Number(contractData.investment.value))))
      setValue("yield", Number(contractData.investment.yield))
      setValue("term", durationInMonths())
      setValue("initDate", contractData.investment.start)
      setValue("endDate", contractData.investment.end)

      setValue("amountQuotes", contractData.investment.quotesAmount)
    }
  }, [contractData])

  useEffect(() => {
    setValue('isSCP', modalityContract === 'scp')
  }, [modalityContract])

  console.log(errors)


  const onSubmit = (data: any) => {

    setLoading(true)    

    toast.info("Editando contrato...")

    const form = new FormData()

    // Payload Principal
    form.append("personType", "PJ")
    form.append("contractType", modalityContract === "scp" ? "SCP" : "MUTUO")
    form.append("role", userProfile.name)

    form.append("bankAccount[accountType]", "CORRENTE")

    // Dados de investimento
    form.append("investment[amount]", String(formatNumberValue(data.value)))
    form.append("investment[monthlyRate]", data.yield)
    form.append("investment[durationInMonths]", data.term)
    form.append("investment[startDate]", `${formatDateToEnglishType(data.initDate)}`)
    form.append("investment[endDate]", `${formatDateToEnglishType(data.endDate)}`)
    form.append("investment[isDebenture]", data.debenture === 's' ? 'true' : 'false')
    if(modalityContract === 'scp') {
      form.append("investment[quotaQuantity]", data.amountQuotes)
    }

    // Dados da compania
    form.append("company[corporateName]", data.name)
    form.append("company[cnpj]", clearLetters(data.document))
    form.append("company[type]", data.companyType)

    form.append("investment[paymentMethod]", "pix")
    form.append("investment[profile]", "moderate")

    // endereço da empresa
    form.append("company[address][street]", data.companyStreet)
    form.append("company[address][city]", data.companyCity)
    form.append("company[address][state]", data.companyState)
    form.append("company[address][neighborhood]", data.companyNeighborhood)
    form.append("company[address][postalCode]", clearLetters(data.companyZipCode))
    form.append("company[address][number]", data.companyNumber)
    form.append("company[address][complement]", data.companyComplement)

    // Representante da empresa
    form.append("company[representative][fullName]", data.ownerName)
    form.append("company[representative][cpf]", clearLetters(data.ownerDocument))
    form.append("company[representative][rg]", data.rg)
    form.append("company[representative][issuingAgency]", data.issuer)
    form.append("company[representative][nationality]", data.placeOfBirth)
    form.append("company[representative][occupation]", data.occupation)
    form.append("company[representative][birthDate]", data.dtBirth)
    form.append("company[representative][email]", data.email)
    form.append("company[representative][phone]", clearLetters(data.phoneNumber))
    form.append("company[representative][motherName]", data.motherName)

    // Endereço do Representante
    form.append("company[representative][address][street]", data.street)
    form.append("company[representative][address][city]", data.city)
    form.append("company[representative][address][state]", data.state)
    form.append("company[representative][address][neighborhood]", data.neighborhood)
    form.append("company[representative][address][postalCode]", clearLetters(data.zipCode))
    form.append("company[representative][address][number]", data.number)
    form.append("company[representative][address][complement]", data.complement)

    // Documentos
    if (contract) {
      form.append('contract', contract[0])
    }
    if (file) {
      form.append('proofOfPayment', file[0])
    }
    if (document) {
      form.append('personalDocument', document[0])
    }
    if (residence) {
      form.append("proofOfResidence", residence[0])
    }
    
    api.put(`/account/resubmit-contract/${id}`, form)
    .then(resp => {
      toast.success('Contrato editado com sucesso!')
      navigation("/meus-contratos")
    })
    .catch(err => {
      returnError(err, "Erro ao cadastrar o contrato!")
    })
    .finally(() => setLoading(false))
  }

  useEffect(() => {
    getCep(zipCode || '')
  }, [zipCode])

  useEffect(() => {
    getCep(companyZipCode || '')
  }, [companyZipCode])

  const getCep = async (cep: string) => {
      const response = await getZipCode(clearLetters(cep))
  
      if(response !== null) {
        setValue("neighborhood", response.neighborhood)
        setValue("city", response.city)
        setValue("state", response.state)
        setValue("street", response.street)
      }
    }

  const getReasons = () => {
    toast.info("Buscando dados da rejeição.", {
      toastId: "searchReasons"
    })
    api.get(`/audit/contract/${id}`).then(resp => {
      const data = resp.data[0].rejectionReasons.reasons
      setReasons(data)
    }).catch(err => returnError(err, "Erro ao buscar os motivos da rejeição")).finally(() => toast.dismiss("searchReasons"))
  }

  useEffect(() => {
    if(reasons?.length > 0) {
      reasons.map(reason => {
        setError(reason.field, {
          type: "required",
          message: reason.reason
        })
      })
    }
  }, [reasons])

  useEffect(() => {
    setValue('initDate', moment().format('YYYY-MM-DD'))
    setStartDate(moment().format('YYYY-MM-DD'))
    getReasons()
  }, [])

  function getFieldDocument(field: 'documentPdf' | 'proofPayment' | 'proofOfResidence' | 'contract') {
    const reason = reasons.find(reason => reason?.field === field) || undefined;
    return reason
  }

  return (
    <div>
      <form action="" onSubmit={handleSubmit(onSubmit)} onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault(); // Bloqueia o envio ao pressionar Enter
          }
        }}>
        <CoverForm title={"Dados Pessoais - Representante"} >
          <div className="flex items-end gap-4 flex-wrap">
            <InputText disableErrorMessage={true} register={register} name="ownerName" width="300px" error={!!errors.ownerName} errorMessage={errors?.ownerName?.message} label="Nome" />
            <InputText disableErrorMessage={true} register={register} name="ownerDocument" width="200px" error={!!errors.ownerDocument} errorMessage={errors?.ownerDocument?.message} label="CPF" setValue={(e) => setValue('ownerDocument', cpfMask(e || ''))} />
            <InputText disableErrorMessage={true} register={register} name="rg" width="200px" error={!!errors.rg} errorMessage={errors?.rg?.message} label="RG" />
            <InputText disableErrorMessage={true} register={register} name="issuer" width="200px" error={!!errors.issuer} errorMessage={errors?.issuer?.message} label="Orgão emissor" />
            <InputText disableErrorMessage={true} register={register} name="placeOfBirth" width="200px" error={!!errors.placeOfBirth} errorMessage={errors?.placeOfBirth?.message} label="Nacionalidade" />
            <InputText disableErrorMessage={true} register={register} name="occupation" width="200px" error={!!errors.occupation} errorMessage={errors?.occupation?.message} label="Ocupação" />
            <InputText disableErrorMessage={true} register={register} name="motherName" width="250px" error={!!errors.motherName} errorMessage={errors?.motherName?.message} label="Nome da mãe" />
            <InputText disableErrorMessage={true} type="date" register={register} name="dtBirth" width="200px" error={!!errors.dtBirth} errorMessage={errors?.dtBirth?.message} label="Data de Nascimento" />
            <InputText disableErrorMessage={true} width="200px" register={register} name="phoneNumber" error={!!errors.phoneNumber} errorMessage={errors?.phoneNumber?.message} label="Celular" maxLength={15} setValue={(e) => setValue('phoneNumber', phoneMask(e || ''))} />
            <InputText disableErrorMessage={true} register={register} name="email" width="300px" error={!!errors.email} errorMessage={errors?.email?.message} label="E-mail" />
            <InputText disableErrorMessage={true} register={register} name="zipCode" width="200px" error={!!errors.zipCode} errorMessage={errors?.zipCode?.message} label="CEP" setValue={(e) => {
              setZipCode(e)
              setValue('zipCode', cepMask(e))
            }} />
            <InputText disableErrorMessage={true} register={register} name="neighborhood" width="300px" error={!!errors.neighborhood} errorMessage={errors?.neighborhood?.message} label="Bairro" />
            <InputText disableErrorMessage={true} register={register} name="street" width="300px" error={!!errors.street} errorMessage={errors?.street?.message} label="Rua" />
            
            <InputText disableErrorMessage={true} register={register} name="city" width="200px" error={!!errors.city} errorMessage={errors?.city?.message} label="Cidade" />
            <InputText disableErrorMessage={true} register={register} maxLength={2} setValue={(e) => setValue("state", String(e).toUpperCase())} name="state" width="150px" error={!!errors.state} errorMessage={errors?.state?.message} label="Estado" />
            <InputText disableErrorMessage={true} register={register} name="number" width="200px" error={!!errors.number} errorMessage={errors?.number?.message} label="Número" />
            <InputText disableErrorMessage={true} register={register} name="complement" width="200px" error={!!errors.complement} errorMessage={errors?.complement?.message} label="Complemento" />
          </div>
        </CoverForm>
          <CoverForm title="Dados da empresa">
            <div className="flex items-end gap-4 flex-wrap">
              <InputText disableErrorMessage={true} register={register} name="name" width="400px" error={!!errors.name} errorMessage={errors?.name?.message} label="Razão Social" />
              <InputText disableErrorMessage={true} register={register} name="document" width="200px" error={!!errors.document} errorMessage={errors?.document?.message} label="CNPJ" setValue={(e) => setValue('document', cnpjMask(e || ''))} /> 
              {/* <InputText disableErrorMessage={true} type="date" register={register} name="dtBirth" width="200px" error={!!errors.dtOpening} errorMessage={errors?.dtOpening?.message} label="Data de Abertura" /> */}
              <InputSelect width="200px" name="companyType" register={register} options={companyTypes} error={!!errors.companyType} errorMessage={errors?.companyType?.message} label="Tipo" />
              <InputText disableErrorMessage={true} register={register} name="companyZipCode" width="200px" error={!!errors.companyZipCode} errorMessage={errors?.companyZipCode?.message} label="CEP" setValue={(e) => {
                setCompanyZipCode(e)
                setValue('companyZipCode', cepMask(e))
              }} />
              <InputText disableErrorMessage={true} register={register} name="companyNeighborhood" width="300px" error={!!errors.companyNeighborhood} errorMessage={errors?.companyNeighborhood?.message} label="Bairro" />
              <InputText disableErrorMessage={true} register={register} name="companyStreet" width="300px" error={!!errors.companyStreet} errorMessage={errors?.companyStreet?.message} label="Rua" />
              <InputText disableErrorMessage={true} register={register} name="companyCity" width="200px" error={!!errors.companyCity} errorMessage={errors?.companyCity?.message} label="Cidade" />
              <InputText disableErrorMessage={true} register={register} maxLength={2} setValue={(e) => setValue("companyState", String(e).toUpperCase())} name="companyState" width="150px" error={!!errors.companyState} errorMessage={errors?.companyState?.message} label="Estado" />
              <InputText disableErrorMessage={true} register={register} name="companyNumber" width="200px" error={!!errors.companyNumber} errorMessage={errors?.companyNumber?.message} label="Número" />
              <InputText disableErrorMessage={true} register={register} name="companyComplement" width="200px" error={!!errors.companyComplement} errorMessage={errors?.companyComplement?.message} label="Complemento" />
            </div>
          </CoverForm>
        <CoverForm title="Dados de Investimento">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText disableErrorMessage={true} register={register} name="value" width="200px" error={!!errors.value} errorMessage={errors?.value?.message} label="Valor" setValue={(e) => setValue('value', valueMask(e || ''))} />
            <InputText disableErrorMessage={true} register={register} type="number" name="term" width="250px" error={!!errors.term} setValue={setInvestDate} errorMessage={errors?.term?.message} label="Prazo investimento - em meses" placeholder="ex: 12" />
            <InputText disableErrorMessage={true} register={register} type="text" name="yield" width="250px" error={!!errors.yield} errorMessage={errors?.yield?.message} label="Taxa Remuneração Mensal - em %" placeholder="ex: 2" />
            {/* <InputSelect disableErrorMessage={true} width="200px" name="purchaseWith" register={register} options={[
              {label: 'PIX', value: 'pix'},
              {label: 'Boleto', value: 'boleto'},
              {label: 'Transferência', value: 'bank_transfer'}
            ]} error={!!errors?.purchaseWith} errorMessage={errors?.purchaseWith?.message} label="Comprar com" /> */}
            {
              modalityContract === 'scp' && <>
                <InputText disableErrorMessage={true} register={register} type="number" name="amountQuotes" width="150px" error={!!errors.amountQuotes} errorMessage={errors?.amountQuotes?.message} label="Quantidade de cotas" />
              </>
            }
            <InputText disableErrorMessage={true} type="date" register={register} maxDate={moment().format('YYYY-MM-DD')} name="initDate" width="200px" setValue={setStartDate} error={!!errors.initDate} errorMessage={errors?.initDate?.message} label="Inicio do contrato" />
            <InputText disableErrorMessage={true} type="date" register={register} name="endDate" width="200px" error={!!errors.endDate} errorMessage={errors?.endDate?.message} label="Final do contrato" />
            <InputSelect disableErrorMessage={true} width="200px" name="companyType" register={register} options={companyTypes} error={!!errors.companyType} errorMessage={errors?.companyType?.message} label="Tipo" />
            {/* <InputSelect width="200px" name="profile" register={register} options={[
              {label: 'Conservador', value: 'conservative'}, 
              {label: 'Moderado', value: 'moderate'},
              {label: 'Agressivo', value: 'aggressive'},
            ]} error={!!errors.profile} errorMessage={errors?.profile?.message} label="Perfil" /> */}
            {/* <InputSelect width="100px" name="debenture" register={register} options={[{label: 'Sim', value: 's'}, {label: 'Não', value: 'n'}]} error={!!errors.debenture} errorMessage={errors?.debenture?.message} label="Debênture" /> */}
          </div>
        </CoverForm>

        <CoverForm title="Anexo de documentos">
          <div>
          <div className='flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white'>
            <div>
              <p className='mb-1'>Contrato</p>
              <Dropzone errorMessage={getFieldDocument('contract')?.reason} disable={!getFieldDocument('contract')?.field} onFileUploaded={setContract} />
            </div>
            <div>
              <p className='mb-1'>Comprovante</p>
              <div>
                <Dropzone disable={!getFieldDocument('proofPayment')?.field} errorMessage={getFieldDocument('proofPayment')?.reason} onFileUploaded={setFile} />
              </div>
            </div>
            <div>
              <p className='mb-1'>Documento de identidade</p>
              <div>
                <Dropzone errorMessage={getFieldDocument('documentPdf')?.reason} disable={!getFieldDocument('documentPdf')?.field} onFileUploaded={setDocumet} />
              </div>
            </div>
            <div>
              <p className='mb-1'>Comprovante de residência</p>
              <div>
                <Dropzone errorMessage={getFieldDocument('proofOfResidence')?.reason} disable={!getFieldDocument('proofOfResidence')?.field} onFileUploaded={setResidence} />
              </div>
            </div>
          </div>
          </div>
        </CoverForm>

        <div className='md:w-52 mb-10'>
          <Button
            label='Enviar'
            size="lg"
            loading={loading}
            disabled={loading}
          />
        </div>
      </form>
    </div>
  )
}