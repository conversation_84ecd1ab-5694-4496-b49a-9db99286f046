"use client";
import { signIn, getSession } from "next-auth/react";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { clearLetters, cnpjMask, cpfMask } from "@/utils/masks";
import { toast } from "react-toastify";
import Input from "@/components/Input";
import { Button } from "@/components/ui/button";
import "../styles/diagonalBackground.css";

export default function LoginPage() {
  const [loading, setLoading] = useState<boolean>(false);
  const router = useRouter();
  const [document, setDocument] = useState("");
  const [password, setPassword] = useState("");

  const documentRef = useRef<HTMLInputElement>(null);
  const passwordRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const checkAuth = async () => {
      const session = await getSession();
      if (session) {
        router.push("/home");
      }
    };
    checkAuth();
  }, [router]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (documentRef.current && documentRef.current.value && document === "") {
        const rawValue = documentRef.current.value;
        const masked =
          rawValue.length <= 14 ? cpfMask(rawValue) : cnpjMask(rawValue);
        setDocument(masked);
      }
      if (passwordRef.current && passwordRef.current.value && password === "") {
        setPassword(passwordRef.current.value);
      }
    }, 100);

    return () => clearTimeout(timeout);
  }, [document, password]);

  const handleSubmit = async () => {
    setLoading(true);

    try {
      const result = await signIn("credentials", {
        document: clearLetters(document),
        password: password,
        redirect: false,
      });

      if (result?.ok) {
        router.push("/home");
      } else {
        toast.error("Usuário ou Senha inválidos!");
      }
    } catch (error) {
      console.error("Erro no login", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="absolute w-full h-screen">
        <div className="absolute inset-0 bg-half-half w-full"></div>
      </div>

      <div className="flex min-h-full h-screen flex-1 flex-col items-center justify-center px-6 py-12 lg:px-8 relative z-10">
        <img
          className="mx-auto h-10 w-auto"
          src="/logo.svg"
          alt="Your Company"
        />
        <div
          className="md:w-4/12 p-14 m-auto bg-opacity-30 backdrop-blur-sm border border-[#FF9900] rounded-lg shadow-2xl shadow-current bg-zinc-900"
          style={{ borderWidth: "1px" }}
        >
          <div className="sm:mx-auto sm:w-full sm:max-w-sm">
            <div>
              <div className="mb-2">
                <Input
                  id="document"
                  label=""
                  name="document"
                  placeholder="Documento"
                  type="text"
                  value={document}
                  onChange={({ target }) => {
                    const { value } = target;
                    if (value.length <= 14) {
                      setDocument(cpfMask(value));
                    } else {
                      setDocument(cnpjMask(value));
                    }
                  }}
                  ref={documentRef}
                />
              </div>
            </div>

            <div>
              <div className="mb-5">
                <Input
                  id="password"
                  label=""
                  name="password"
                  placeholder="Senha"
                  type="text"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSubmit();
                    }
                  }}
                  ref={passwordRef}
                  value={password}
                  onChange={({ target }) => {
                    setPassword(target.value);
                  }}
                />
              </div>
            </div>

            <div>
              <Button
                onClick={handleSubmit}
                loading={loading}
                disabled={
                  document.length === 0 || password.length === 0 || loading
                }
                size="lg"
                className="w-full"
              >
                Entrar
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
