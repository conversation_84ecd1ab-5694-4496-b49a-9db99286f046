"use client";

import InputText from "@/components/Inputs/InputText";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import Button from "@/components/Button/Button";
import {
  cepMask,
  clearLetters,
  cpfMask,
  phoneMask,
  valueMask,
} from "@/utils/masks";
import { createPFContract } from "@/utils/schemas/createContract";
import { useEffect, useState } from "react";
import InputSelect from "@/components/Inputs/InputSelect";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import { toast } from "react-toastify";
import SelectSearch from "@/components/SelectSearch";
import Input from "@/components/Input";
import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import CoverForm from "@/components/CoverForm";
import { getFinalDataWithMount } from "@/functions/getDataFilter";
import moment from "moment";
import formatNumberValue from "@/utils/formatNumberValue";
import { isValidateCPF } from "@/utils/validate-documents";
import { isUnderage } from "@/utils/isUnderage";
import { getUserProfile } from "@/functions/getUserData";
import validatePhoneNumber from "@/utils/validatePhoneNumber";
import isValidUF from "@/utils/isValidUf";
import Dropzone from "@/components/Dropzone";
import { getZipCode } from "@/functions/getZipCode";
import { useParams } from "next/navigation";
import { InferType } from "yup";
import { ContractPF } from "@/app/contratos/contrato/types";
import { editPFContract } from "@/utils/schemas/editContract";
import { formatDateToEnglishType } from "@/functions/formatDate";
import { useNavigation } from "@/hooks/navigation";
import { cleanValue } from "@/utils/formatValue";

interface IProps {
  modalityContract: "mutuo" | "scp";
  contractData?: ContractPF;
}

interface AdvisorsTableItem {
  id: string;
  taxValue: number | string;
  generatedId: string;
}

interface BrokerAcessor {
  name: string;
  id: string;
  rate: string;
}

type PFContractFields = keyof InferType<typeof editPFContract>;

interface FieldReason {
  field: PFContractFields;
  reason: string;
}

export default function PhysicalEditing({
  modalityContract,
  contractData,
}: IProps) {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [zipCode, setZipCode] = useState("");
  const [startDate, setStartDate] = useState("");
  const [investDate, setInvestDate] = useState("");
  const [reasons, setReasons] = useState<FieldReason[]>([]);

  const { navigation } = useNavigation();

  const [acessors, setAcessors] = useState<BrokerAcessor[]>([]);

  const [brokers, setBrokers] = useState<BrokerAcessor[]>([]);
  const [brokerIdSelected, setBrokerIdSelected] = useState("");

  const [loadingAcessors, setLoadingAcessors] = useState(false);
  const [advisors, setAdvisors] = useState<AdvisorsTableItem[]>([]);

  const [contract, setContract] = useState<FileList>();
  const [file, setFile] = useState<FileList>();
  const [document, setDocumet] = useState<FileList>();
  const [residence, setResidence] = useState<FileList>();

  const userProfile = getUserProfile();

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(editPFContract),
  });

  function durationInMonths() {
    const start = moment(contractData?.investment.start);
    const end = moment(contractData?.investment.end);

    return String(end.diff(start, "months"));
  }

  useEffect(() => {
    if (contractData?.investor) {
      setValue("name", contractData.investor.name);
      setValue("document", cpfMask(contractData.investor.document));
      setValue("rg", contractData.investor.rg);
      setValue("issuer", contractData.investor.issuingAgency);
      setValue("placeOfBirth", contractData.investor.nationality);
      setValue("occupation", contractData.investor.occupation);
      setValue("phoneNumber", phoneMask(contractData.investor.phone));
      setValue("dtBirth", contractData.investor.birthDate);
      setValue("email", contractData.investor.email);
      setValue("motherName", contractData.investor.motherName);
      setValue("zipCode", contractData.investor.address.zipcode);
      setValue("neighborhood", contractData.investor.address.neighborhood);
      setValue("street", contractData.investor.address.street);
      setValue("city", contractData.investor.address.city);
      setValue("state", contractData.investor.address.state);
      setValue("number", contractData.investor.address.number);
      setValue("complement", contractData.investor.address.complement);
      setValue("value", cleanValue(Number(contractData.investment.value)));
      setValue("yield", Number(contractData.investment.yield));
      setValue("term", durationInMonths());
      setValue("initDate", contractData.investment.start);
      setValue("endDate", contractData.investment.end);
      setValue("purchasedWith", contractData.investment.purchasedWith);
    }
  }, [contractData]);

  useEffect(() => {
    setValue("isSCP", modalityContract === "scp");
  }, [modalityContract]);

  useEffect(() => {
    getReasons();
  }, []);

  const onSubmit = (data: any) => {
    setLoading(true);

    const form = new FormData();

    form.append("role", userProfile.name);

    form.append("personType", "PF");
    form.append("contractType", modalityContract === "scp" ? "SCP" : "MUTUO");

    // Dados do investidor
    form.append("individual[fullName]", String(data.name).trim());
    form.append("individual[cpf]", clearLetters(data.document).trim());
    form.append("individual[rg]", data.rg);
    form.append("individual[issuingAgency]", data.issuer);
    form.append("individual[nationality]", data.placeOfBirth);
    form.append("individual[occupation]", data.occupation);
    form.append("individual[birthDate]", data.dtBirth);
    form.append("individual[email]", data.email);
    form.append("individual[phone]", `${clearLetters(data.phoneNumber)}`);
    form.append("individual[motherName]", data.motherName);

    // Endereço
    form.append("individual[address][street]", data.street);
    form.append("individual[address][neighborhood]", data.neighborhood);
    form.append("individual[address][city]", data.city);
    form.append("individual[address][state]", data.state);
    form.append("individual[address][postalCode]", clearLetters(data.zipCode));
    form.append("individual[address][number]", data.number);
    form.append("individual[address][complement]", data.complement);

    form.append("investment[profile]", "moderate");

    // Dados do investimento
    form.append("investment[amount]", String(formatNumberValue(data.value)));
    form.append("investment[monthlyRate]", data.yield);
    form.append("investment[durationInMonths]", data.term);
    form.append("investment[paymentMethod]", "pix");
    form.append(
      "investment[startDate]",
      `${formatDateToEnglishType(data.initDate)}`
    );
    form.append(
      "investment[endDate]",
      `${formatDateToEnglishType(data.endDate)}`
    );

    form.append("investment[isDebenture]", String(data.debenture === "s"));
    if (modalityContract === "scp") {
      form.append("investment[quotaQuantity]", data.amountQuotes);
    }

    // Documentos
    if (contract) {
      form.append("contract", contract[0]);
    }
    if (file) {
      form.append("proofOfPayment", file[0]);
    }
    if (document) {
      form.append("personalDocument", document[0]);
    }
    if (residence) {
      form.append("proofOfResidence", residence[0]);
    }

    api
      .put(`/account/resubmit-contract/${id}`, form)
      .then((resp) => {
        toast.success("Contrato editado com sucesso!");
        navigation("/meus-contratos");
      })
      .catch((err) => {
        returnError(err, "Erro ao cadastrar o contrato!");
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    getCep(zipCode || "");
  }, [zipCode]);

  const getCep = async (cep: string) => {
    const response = await getZipCode(clearLetters(cep));

    if (response !== null) {
      setValue("neighborhood", response.neighborhood);
      setValue("city", response.city);
      setValue("state", response.state);
      setValue("street", response.street);
    }
  };

  const getReasons = () => {
    toast.info("Buscando dados da rejeição.", {
      toastId: "searchReasons",
    });
    api
      .get(`/audit/contract/${id}`)
      .then((resp) => {
        const data = resp.data[0].rejectionReasons.reasons;
        setReasons(data);
      })
      .catch((err) => returnError(err, "Erro ao buscar os motivos da rejeição"))
      .finally(() => toast.dismiss("searchReasons"));
  };

  useEffect(() => {
    console.log(reasons);
    if (reasons?.length > 0) {
      reasons.map((reason) => {
        setError(reason.field, {
          type: "required",
          message: reason.reason,
        });
      });
    }
  }, [reasons]);

  useEffect(() => {
    if (userProfile.name !== "broker" && userProfile.name !== "advisor") {
      getBrokers();
    }
  }, []);

  function getFieldDocument(
    field: "documentPdf" | "proofPayment" | "proofOfResidence" | "contract"
  ) {
    const reason =
      reasons.find((reason) => reason?.field === field) || undefined;
    return reason;
  }

  function getBrokers() {
    api
      .get(
        userProfile.name === "superadmin"
          ? "/wallets/list-brokers"
          : "/wallets/admin/brokers"
      )
      .then((resp) => {
        setBrokers(resp.data);
      })
      .catch((error) => {
        returnError(error, "Erro ao buscar a lista de brokers");
      });
  }
  

  return (
    <div>
      <form
        action=""
        onSubmit={handleSubmit(onSubmit)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault(); // Bloqueia o envio ao pressionar Enter
          }
        }}
      >
        <CoverForm title="Dados Pessoais">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              disableErrorMessage={true}
              register={register}
              name="name"
              width="300px"
              error={!!errors.name}
              errorMessage={errors?.name?.message}
              label="Nome completo"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="document"
              width="200px"
              error={!!errors.document}
              errorMessage={errors?.document?.message}
              label="CPF"
              setValue={(e) => setValue("document", cpfMask(e || ""))}
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="rg"
              width="200px"
              error={!!errors.rg}
              errorMessage={errors?.rg?.message}
              label="Identidade"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="issuer"
              width="200px"
              error={!!errors.issuer}
              errorMessage={errors?.issuer?.message}
              label="Orgão emissor"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="placeOfBirth"
              width="200px"
              error={!!errors.placeOfBirth}
              errorMessage={errors?.placeOfBirth?.message}
              label="Nacionalidade"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="occupation"
              width="200px"
              error={!!errors.occupation}
              errorMessage={errors?.occupation?.message}
              label="Ocupação"
            />
            <InputText
              disableErrorMessage={true}
              width="200px"
              register={register}
              name="phoneNumber"
              maxLength={15}
              error={!!errors.phoneNumber}
              errorMessage={errors?.phoneNumber?.message}
              label="Celular"
              setValue={(e) => setValue("phoneNumber", phoneMask(e || ""))}
            />
            <InputText
              disableErrorMessage={true}
              type="date"
              register={register}
              name="dtBirth"
              width="200px"
              error={!!errors.dtBirth}
              errorMessage={errors?.dtBirth?.message}
              label="Data de Nascimento"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="email"
              width="300px"
              error={!!errors.email}
              errorMessage={errors?.email?.message}
              label="E-mail"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="motherName"
              width="300px"
              error={!!errors.motherName}
              errorMessage={errors?.motherName?.message}
              label="Nome da mãe"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="zipCode"
              width="200px"
              error={!!errors.zipCode}
              errorMessage={errors?.zipCode?.message}
              label="CEP"
              setValue={(e) => {
                setZipCode(e);
                setValue("zipCode", cepMask(e));
              }}
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="neighborhood"
              width="200px"
              error={!!errors.neighborhood}
              errorMessage={errors?.neighborhood?.message}
              label="Bairro"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="street"
              width="300px"
              error={!!errors.street}
              errorMessage={errors?.street?.message}
              label="Rua"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="city"
              width="200px"
              error={!!errors.city}
              errorMessage={errors?.city?.message}
              label="Cidade"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              maxLength={2}
              setValue={(e) => setValue("state", String(e).toUpperCase())}
              name="state"
              width="150px"
              error={!!errors.state}
              errorMessage={errors?.state?.message}
              label="Estado"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="number"
              width="200px"
              error={!!errors.number}
              errorMessage={errors?.number?.message}
              label="Número"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              name="complement"
              width="200px"
              error={!!errors.complement}
              errorMessage={errors?.complement?.message}
              label="Complemento"
            />
          </div>
        </CoverForm>
        <CoverForm title="Dados de Investimento">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText
              disableErrorMessage={true}
              register={register}
              name="value"
              width="200px"
              error={!!errors.value}
              errorMessage={errors?.value?.message}
              label="Valor"
              setValue={(e) => setValue("value", valueMask(e))}
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              type="number"
              name="term"
              width="250px"
              setValue={setInvestDate}
              error={!!errors.term}
              errorMessage={errors?.term?.message}
              label="Prazo investimento - em meses"
              placeholder="ex: 12"
            />
            <InputText
              disableErrorMessage={true}
              register={register}
              type="text"
              name="yield"
              width="250px"
              error={!!errors.yield}
              errorMessage={errors?.yield?.message}
              label="Taxa Remuneração Mensal - em %"
              placeholder="ex: 2"
            />
            <InputSelect
              width="200px"
              name="purchasedWith"
              register={register}
              options={[
                { label: "PIX", value: "pix" },
                { label: "Boleto", value: "boleto" },
                { label: "Transferência", value: "bank_transfer" },
              ]}
              error={!!errors?.purchasedWith}
              errorMessage={errors?.purchasedWith?.message}
              label="Comprar com"
            />
            {modalityContract === "scp" && (
              <>
                <InputText
                  disableErrorMessage={true}
                  register={register}
                  type="number"
                  name="amountQuotes"
                  width="150px"
                  error={!!errors.amountQuotes}
                  errorMessage={errors?.amountQuotes?.message}
                  label="Quantidade de cotas"
                />
              </>
            )}
            <InputText
              disableErrorMessage={true}
              type="date"
              register={register}
              name="initDate"
              width="200px"
              setValue={setStartDate}
              error={!!errors.initDate}
              errorMessage={errors?.initDate?.message}
              label="Inicio do contrato"
            />
            <InputText
              disableErrorMessage={true}
              type="date"
              register={register}
              name="endDate"
              width="200px"
              error={!!errors.endDate}
              errorMessage={errors?.endDate?.message}
              label="Final do contrato"
            />
            {/* <InputSelect width="200px" name="profile" register={register} options={[
              {label: 'Conservador', value: 'conservative'}, 
              {label: 'Moderado', value: 'moderate'},
              {label: 'Agressivo', value: 'aggressive'},
            ]} error={!!errors.profile} errorMessage={errors?.profile?.message} label="Perfil" /> */}
            {/* <InputSelect width="100px" name="debenture" register={register} options={[{label: 'Sim', value: 's'}, {label: 'Não', value: 'n'}]} error={!!errors.debenture} errorMessage={errors?.debenture?.message} label="Debênture" /> */}
          </div>
        </CoverForm>
        {/* <CoverForm title="Dados bancarios">
          <div className="flex items-end gap-4 flex-wrap">
            <InputText disableErrorMessage={true} register={register} name="bank" width="300px" error={!!errors.bank} errorMessage={errors?.bank?.message} label="Banco" />
            <InputText disableErrorMessage={true} register={register} name="agency" width="200px" error={!!errors.agency} errorMessage={errors?.agency?.message} label="Agência" />
            <InputText disableErrorMessage={true} register={register} name="accountNumber" width="200px" error={!!errors.accountNumber} errorMessage={errors?.accountNumber?.message} label="Conta" />
            <InputText disableErrorMessage={true} register={register} name="pix" width="250px" error={!!errors.pix} errorMessage={errors?.pix?.message} label="Pix" />
          </div>
        </CoverForm> */}
        <CoverForm title="Anexo de documentos">
          <div>
          <div className='flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white'>
            <div>
              <p className='mb-1'>Contrato</p>
              <Dropzone errorMessage={getFieldDocument('contract')?.reason} disable={!getFieldDocument('contract')?.field} onFileUploaded={setContract} />
            </div>
            <div>
              <p className='mb-1'>Comprovante</p>
              <div>
                <Dropzone disable={!getFieldDocument('proofPayment')?.field} errorMessage={getFieldDocument('proofPayment')?.reason} onFileUploaded={setFile} />
              </div>
            </div>
            <div>
              <p className='mb-1'>Documento de identidade</p>
              <div>
                <Dropzone errorMessage={getFieldDocument('documentPdf')?.reason} disable={!getFieldDocument('documentPdf')?.field} onFileUploaded={setDocumet} />
              </div>
            </div>
            <div>
              <p className='mb-1'>Comprovante de residência</p>
              <div>
                <Dropzone errorMessage={getFieldDocument('proofOfResidence')?.reason} disable={!getFieldDocument('proofOfResidence')?.field} onFileUploaded={setResidence} />
              </div>
            </div>
          </div>
          </div>
        </CoverForm>
        <div className="md:w-52 mb-10">
          <Button
            label="Enviar"
            loading={loading}
            size="lg"
            disabled={loading}
          />
        </div>
      </form>
    </div>
  );
}
