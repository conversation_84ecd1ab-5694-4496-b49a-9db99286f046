import Button from "@/components/Button/Button"
import StatusWithDescription from "@/components/StatusWithDescription"
import api from "@/core/api"
import formatDate from "@/functions/formatDate"
import { formatStatusAditive } from "@/functions/formatStatus"
import { getUserProfile } from "@/functions/getUserData"
import returnError from "@/functions/returnError"
import Contact, { Addendum } from "@/models/contract"
import { ArrowTopRightOnSquareIcon, ExclamationCircleIcon } from "@heroicons/react/24/outline"
import { useCallback, useEffect, useState } from "react"
import { useDropzone } from "react-dropzone"
import { toast } from "react-toastify"
interface IProps {
  contract: Contact
  aditives: Addendum[]
  getAditives: () => void
}

export default function AditiveData({ contract, aditives, getAditives }: IProps) {
  const [selectedFile, setSelectedFile] = useState<FileList>()
  const [addendumId, setAddendumId] = useState<number>()
  const [loading, setLoading] = useState(false)
  const userProfile = getUserProfile()

  useEffect(() => {
    if (addendumId && selectedFile) {
      onUploadFile();
    }
  }, [addendumId, selectedFile]);

  const onDrop = useCallback((acceptedFiles: any) => {
    const file = acceptedFiles

    setSelectedFile(file)
  }, [])

  const onUploadFile = () => {
    if(!selectedFile) {
      return toast.warning('Selecione um comprovante para anexar!')
    }
    setLoading(true)
    toast.info('Enviando comprovante...')
    const form = new FormData()
    form.append('addendumId', String(addendumId))
    if (selectedFile) {
      form.append('proofPayment', selectedFile[0])
    }

    api.post('/contract/addendum/proof-payment', form,).then(resp => {
      toast.success('Comprovante anexado com sucesso!')
      getAditives()
      setSelectedFile(undefined)
    }).catch(err => {
      returnError(err, "Erro ao enviar comprovante")
    }).finally(() => {
      setLoading(false)
    })
  }

  const {
      getRootProps,
      getInputProps,
    } = useDropzone({
      onDrop,
      maxFiles: 1,
      multiple: false,
      onError: (error) => {
        if ( error.message === `Failed to execute 'createObjectURL' on 'URL': Overload resolution failed.`) {
          toast.warning('Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb')
        }
      },
      accept: {
        'image/*': ['.png', '.jpeg', '.pdf'],
      },
      onFileDialogCancel: () => {
        setSelectedFile(undefined)
      },
      disabled: loading
    })

  const returnAddendumFiles = ({files, type}: {
    files:{
      type: string,
      url: string
    }[],
    type: 'ADDENDUM' | 'PAYMENT'
    addendumId?: number
  }) => {
    const file = files.filter(file => file.type === type)[0]
    if(file) {
      return (
        <p className="w-full flex items-center justify-center">
          <ArrowTopRightOnSquareIcon className="cursor-pointer" onClick={() => {
            window.open(file.url, '_blank')
          }} color={'#fff'} width={20} />
        </p>
      )
    }

    if(!file && type === 'PAYMENT' && (userProfile.name === 'broker' || userProfile.name === 'advisor') ) return (
      <div {...getRootProps()} >
        <input {...getInputProps()} disabled={loading} accept=".png,.jpg,.pdf" />
        <p className={`w-full flex items-center justify-center bg-orange-linear py-1 rounded-lg text-sm ${loading ? 'opacity-50' : 'cursor-pointer'}`}>  
          Anexar
        </p>
      </div>
    )
    return (
      <p className="w-full flex items-center justify-center">
        <ExclamationCircleIcon color={'#FF9900'} width={20} />
      </p>
    )
  
  }

  const renderTable = () => {
    return (
      <table className='w-full relative min-h-20'>
          <thead className='w-full bg-[#313131] border-y border-y-[#FF9900]'>
            <tr className='w-full py-2'>
              <th className="min-w-[100px]">
                <p className='font-bold text-sm'>Valor</p>
              </th>
              <th className="min-w-[150px]">
                <p className='font-bold text-sm'>Rendimento</p>
              </th>
              <th className="min-w-[250px]">
                <p className='font-bold text-sm'>Consultor</p>
              </th>
              <th className="min-w-[150px]">
                <p className='font-bold text-sm'>Criado em</p>
              </th>
              <th className="min-w-[100px]">
                <p className='font-bold text-sm'>Assinatura</p>
              </th>
              <th className="min-w-[100px]">
                <p className='font-bold text-sm'>Contrato</p>
              </th>
              <th className="min-w-[120px]">
                <p className='font-bold text-sm'>Comprovante</p>
              </th>
            </tr>
          </thead>
            {
              aditives.length >= 1 ? (
                <tbody className='w-full'>
                {
                  aditives.map((t, index) => {
                    return (
                      <tr key={index} className={`border-b-[1px] border-b-black h-10`}>
                        <td><p className='text-xs text-center'>{
                          Number(t.value || 0).toLocaleString('pt-br', {
                            style: 'currency',
                            currency: 'BRL'
                          })
                        }</p></td>
                        <td><p className='text-xs text-center'>{t.yieldRate || '0'}%</p></td>
                        <td><p className='text-xs text-center'>{contract.consultorResponsavel}</p></td>
                        <td><p className='text-xs text-center'>{formatDate(t.applicationDate)}</p></td>
                        <td className="select-none">
                          <StatusWithDescription 
                            description={formatStatusAditive(t.status).description}
                            text={formatStatusAditive(t.status).title}
                            textColor={formatStatusAditive(t.status).textColor}
                          />
                        </td>
                        <td>
                          {returnAddendumFiles({
                            files: t?.addendumFiles,
                            type: 'ADDENDUM'
                          })}
                        </td>
                        <td onClick={() => setAddendumId(t.id)}>
                          {returnAddendumFiles({
                            files: t?.addendumFiles,
                            type: 'PAYMENT',
                            addendumId: t.id
                          })}
                        </td>
                      </tr>
                    )
                  })
                }
                </tbody>
              ) : (
                <div className='text-center mt-5 absolute w-full'>
                  <p>Nenhum dado encontrado</p>
                </div>
              )
            }
        </table>
    )
  }
  
  return (
    <div className="mt-5">
      {renderTable()}
    </div>
  )
}