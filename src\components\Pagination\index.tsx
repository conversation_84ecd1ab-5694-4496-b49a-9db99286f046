import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

interface IProps {
  page: number;
  setPage: (d: number) => void;
  perPage: number;
  lastPage: number;
  totalItems: string;
  loading?: boolean; // Adicionamos um prop de loading
}

export default function Pagination({ lastPage, page, setPage, totalItems, perPage, loading = false }: IProps) {
  // Se estiver carregando ou se lastPage for NaN, não renderiza o componente
  if (loading || isNaN(lastPage) || lastPage === 0) {
    return (
      <div className="w-full flex justify-between items-center pr-5 border-t border-[#FF9900]">
        <div />
        <div>
          <p className="text-sm">Carregando...</p>
        </div>
        <div className="py-2 flex gap-2">
          {/* Botões desabilitados durante o carregamento */}
          <div className="flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed">
            <ChevronLeftIcon color="#555" width={15} />
          </div>
          <div className="flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818]">...</div>
          <div className="flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed">
            <ChevronRightIcon color="#555" width={15} />
          </div>
        </div>
      </div>
    );
  }

  const renderPages = () => {
    const pages = new Set<number>();

    pages.add(1);
    pages.add(lastPage);

    if (page > 1) pages.add(page - 1);
    pages.add(page);
    if (page < lastPage) pages.add(page + 1);

    // Converter para array e ordenar
    const sortedPages = Array.from(pages).sort((a, b) => a - b);

    return sortedPages.map((num, idx, arr) => {
      return (
      <div key={num} className="flex items-center gap-2">
        {idx > 0 && arr[idx - 1] !== num - 1 && (
        <div
          className={`flex items-center justify-center`}
        >
          ...
        </div>
        )}
        {
          Number.isFinite(num) && (
            <div
              className={`flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer bg-[#262626] ${
                page === num ? "text-[#FF9900]" : "text-white"
              }`}
              onClick={() => setPage(num)}
            >
              {num}
            </div>
          )
        }
      </div>
    )
    });
  };

  const getDisplayedItems = () => {
    const total = parseInt(totalItems, 10);
    if (total === 0) return "Nenhum resultado";

    const startItem = (page - 1) * perPage + 1;
    const endItem = Math.min(page * perPage, total);
    return `Exibindo ${startItem} a ${endItem} de ${total} resultados`;
  };

  return (
    <div className="w-full flex justify-between items-center pr-5 border-t border-[#FF9900]">
      <div />
      <div>
        <p className="text-sm">{getDisplayedItems()}</p>
      </div>
      <div className="py-2 flex gap-2">
        {/* Botão Anterior */}
        <div
          className={`flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ${
            page > 1 ? "bg-[#262626]" : "bg-[#181818] cursor-not-allowed"
          }`}
          onClick={() => page > 1 && setPage(page - 1)}
        >
          <ChevronLeftIcon color="#fff" width={15} />
        </div>

        {/* Números das páginas */}
        <div className="flex gap-2 select-none">{renderPages()}</div>

        {/* Botão Próximo */}
        <div
          className={`flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ${
            page < lastPage ? "bg-[#262626]" : "bg-[#181818] cursor-not-allowed"
          }`}
          onClick={() => page < lastPage && setPage(page + 1)}
        >
          <ChevronRightIcon color="#fff" width={15} />
        </div>
      </div>
    </div>
  );
}
